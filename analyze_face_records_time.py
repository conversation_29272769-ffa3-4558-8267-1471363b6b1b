#!/usr/bin/env python3
"""
分析face类型考勤记录的时间分布，为转换为check_in/check_out做准备
"""
import sqlite3
from datetime import datetime
from collections import defaultdict
from admin.db_init import DATABASE

def analyze_face_records_time():
    """分析face类型记录的时间分布"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    
    try:
        print("分析face类型考勤记录的时间分布")
        print("=" * 80)
        
        # 获取所有face类型的记录
        c.execute("""
            SELECT a.id, a.person_id, a.timestamp, a.status, p.name as person_name
            FROM attendance a
            LEFT JOIN persons p ON a.person_id = p.id
            WHERE a.check_type = 'face'
            ORDER BY a.timestamp
        """)
        face_records = c.fetchall()
        
        print(f"总共找到 {len(face_records)} 条face类型记录")
        
        # 按小时统计记录分布
        hour_distribution = defaultdict(int)
        person_daily_records = defaultdict(list)  # 按人员和日期分组
        
        for record in face_records:
            timestamp = datetime.strptime(record['timestamp'], '%Y-%m-%d %H:%M:%S')
            hour = timestamp.hour
            date = timestamp.date()
            person_id = record['person_id']
            
            hour_distribution[hour] += 1
            person_daily_records[(person_id, date)].append({
                'id': record['id'],
                'timestamp': timestamp,
                'status': record['status'],
                'person_name': record['person_name']
            })
        
        # 显示时间分布
        print(f"\n按小时统计的记录分布:")
        print("-" * 40)
        for hour in sorted(hour_distribution.keys()):
            count = hour_distribution[hour]
            bar = "█" * (count // 5)  # 每5条记录一个方块
            print(f"{hour:2d}:00 | {count:3d} 条 | {bar}")
        
        # 分析每人每天的打卡模式
        print(f"\n每人每天的打卡记录分析:")
        print("-" * 80)
        
        conversion_plan = []
        stats = {
            'single_records': 0,
            'double_records': 0,
            'multiple_records': 0,
            'morning_checkins': 0,
            'afternoon_checkouts': 0
        }
        
        for (person_id, date), records in person_daily_records.items():
            records.sort(key=lambda x: x['timestamp'])
            person_name = records[0]['person_name'] or f"Person_{person_id}"
            
            print(f"{person_name} - {date} ({len(records)}条记录):")
            
            if len(records) == 1:
                # 单条记录：根据时间判断是上班还是下班
                record = records[0]
                hour = record['timestamp'].hour
                
                if hour < 12:  # 上午记录视为上班打卡
                    new_type = 'check_in'
                    stats['morning_checkins'] += 1
                else:  # 下午记录视为下班打卡
                    new_type = 'check_out'
                    stats['afternoon_checkouts'] += 1
                
                conversion_plan.append({
                    'id': record['id'],
                    'old_type': 'face',
                    'new_type': new_type,
                    'timestamp': record['timestamp'],
                    'reason': f"单条记录，{hour}点{'上班' if new_type == 'check_in' else '下班'}打卡"
                })
                
                print(f"  {record['timestamp'].strftime('%H:%M:%S')} -> {new_type} ({record['status']})")
                stats['single_records'] += 1
                
            elif len(records) == 2:
                # 两条记录：第一条为上班，第二条为下班
                first_record = records[0]
                second_record = records[1]
                
                conversion_plan.append({
                    'id': first_record['id'],
                    'old_type': 'face',
                    'new_type': 'check_in',
                    'timestamp': first_record['timestamp'],
                    'reason': "两条记录中的第一条，上班打卡"
                })
                
                conversion_plan.append({
                    'id': second_record['id'],
                    'old_type': 'face',
                    'new_type': 'check_out',
                    'timestamp': second_record['timestamp'],
                    'reason': "两条记录中的第二条，下班打卡"
                })
                
                print(f"  {first_record['timestamp'].strftime('%H:%M:%S')} -> check_in ({first_record['status']})")
                print(f"  {second_record['timestamp'].strftime('%H:%M:%S')} -> check_out ({second_record['status']})")
                stats['double_records'] += 1
                stats['morning_checkins'] += 1
                stats['afternoon_checkouts'] += 1
                
            else:
                # 多条记录：第一条为上班，最后一条为下班，中间的保持原状或删除
                first_record = records[0]
                last_record = records[-1]
                
                conversion_plan.append({
                    'id': first_record['id'],
                    'old_type': 'face',
                    'new_type': 'check_in',
                    'timestamp': first_record['timestamp'],
                    'reason': "多条记录中的第一条，上班打卡"
                })
                
                conversion_plan.append({
                    'id': last_record['id'],
                    'old_type': 'face',
                    'new_type': 'check_out',
                    'timestamp': last_record['timestamp'],
                    'reason': "多条记录中的最后一条，下班打卡"
                })
                
                print(f"  {first_record['timestamp'].strftime('%H:%M:%S')} -> check_in ({first_record['status']})")
                for i, record in enumerate(records[1:-1], 1):
                    print(f"  {record['timestamp'].strftime('%H:%M:%S')} -> 保持face (中间记录{i})")
                print(f"  {last_record['timestamp'].strftime('%H:%M:%S')} -> check_out ({last_record['status']})")
                
                stats['multiple_records'] += 1
                stats['morning_checkins'] += 1
                stats['afternoon_checkouts'] += 1
        
        print(f"\n转换统计:")
        print("-" * 40)
        print(f"单条记录天数: {stats['single_records']}")
        print(f"两条记录天数: {stats['double_records']}")
        print(f"多条记录天数: {stats['multiple_records']}")
        print(f"将转换为check_in: {stats['morning_checkins']} 条")
        print(f"将转换为check_out: {stats['afternoon_checkouts']} 条")
        print(f"总计需要转换: {len(conversion_plan)} 条记录")
        
        return conversion_plan
        
    except Exception as e:
        print(f"分析时出错: {str(e)}")
        return []
    finally:
        conn.close()

if __name__ == "__main__":
    conversion_plan = analyze_face_records_time()
    
    if conversion_plan:
        print(f"\n转换计划已生成，共 {len(conversion_plan)} 条记录需要转换")
        print("可以运行转换脚本来执行实际的数据库更新操作")
    else:
        print("未生成转换计划")
