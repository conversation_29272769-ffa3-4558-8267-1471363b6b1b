#!/usr/bin/env python3
"""
详细分析空表在代码中的实际使用情况
"""
import sqlite3
import os
import re

DATABASE = "face_recognition.db"

def analyze_specific_table_usage():
    """分析特定表的实际使用情况"""
    
    # 重点分析的空表
    empty_tables = [
        'permissions', 'user_roles', 'activity_logs', 'approval_workflows',
        'approval_steps', 'attendance_corrections', 'department_rules',
        'overtime_requests', 'permission_groups', 'report_templates',
        'rule_details', 'user_groups'
    ]
    
    print("详细空表使用情况分析")
    print("=" * 80)
    
    for table in empty_tables:
        print(f"\n🔍 分析表: {table}")
        print("-" * 50)
        
        # 分析代码中的使用情况
        usage_analysis = analyze_table_in_code(table)
        
        # 显示分析结果
        display_usage_analysis(table, usage_analysis)

def analyze_table_in_code(table_name):
    """分析表在代码中的具体使用情况"""
    usage = {
        'create_statements': [],
        'insert_operations': [],
        'select_operations': [],
        'update_operations': [],
        'delete_operations': [],
        'join_operations': [],
        'references': [],
        'total_files': 0
    }
    
    # 搜索模式
    patterns = {
        'create': rf'CREATE\s+TABLE.*{table_name}',
        'insert': rf'INSERT\s+INTO\s+{table_name}',
        'select': rf'SELECT.*FROM\s+{table_name}',
        'update': rf'UPDATE\s+{table_name}',
        'delete': rf'DELETE\s+FROM\s+{table_name}',
        'join': rf'JOIN\s+{table_name}',
        'reference': table_name
    }
    
    # 搜索文件
    search_dirs = ['admin', 'user', 'templates', '.']
    extensions = ['.py', '.html', '.js', '.sql']
    
    for search_dir in search_dirs:
        if not os.path.exists(search_dir):
            continue
            
        if os.path.isfile(search_dir):
            files_to_check = [search_dir]
        else:
            files_to_check = []
            for root, _, files in os.walk(search_dir):
                if '__pycache__' in root:
                    continue
                for file in files:
                    if any(file.endswith(ext) for ext in extensions):
                        files_to_check.append(os.path.join(root, file))
        
        for file_path in files_to_check:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    
                    if table_name in content:
                        usage['total_files'] += 1
                        
                        # 检查各种操作
                        for op_type, pattern in patterns.items():
                            matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
                            if matches:
                                if op_type == 'reference':
                                    # 查找包含表名的行
                                    lines = content.split('\n')
                                    for i, line in enumerate(lines, 1):
                                        if table_name in line and not line.strip().startswith('#'):
                                            usage['references'].append(f"{file_path}:{i}: {line.strip()}")
                                else:
                                    for match in matches:
                                        usage[f'{op_type}_operations'].append(f"{file_path}: {match}")
            
            except Exception:
                pass
    
    return usage

def display_usage_analysis(table_name, usage):
    """显示使用情况分析结果"""
    
    # 统计各种操作
    create_count = len(usage['create_statements'])
    insert_count = len(usage['insert_operations'])
    select_count = len(usage['select_operations'])
    update_count = len(usage['update_operations'])
    delete_count = len(usage['delete_operations'])
    join_count = len(usage['join_operations'])
    
    print(f"📊 操作统计:")
    print(f"  - 创建表语句: {create_count}")
    print(f"  - INSERT操作: {insert_count}")
    print(f"  - SELECT操作: {select_count}")
    print(f"  - UPDATE操作: {update_count}")
    print(f"  - DELETE操作: {delete_count}")
    print(f"  - JOIN操作: {join_count}")
    print(f"  - 引用文件数: {usage['total_files']}")
    
    # 判断使用状态
    if insert_count > 0 or select_count > 0 or update_count > 0 or delete_count > 0:
        status = "🟢 实际使用中"
        recommendation = "保留 - 表正在被业务逻辑使用"
    elif join_count > 0:
        status = "🟡 部分使用"
        recommendation = "保留 - 表被其他查询引用"
    elif create_count > 0:
        status = "🟠 仅定义"
        recommendation = "可选保留 - 表已定义但未使用"
    else:
        status = "🔴 未使用"
        recommendation = "可以删除 - 表未被引用"
    
    print(f"📈 使用状态: {status}")
    print(f"💡 建议: {recommendation}")
    
    # 显示具体的操作示例
    if usage['insert_operations']:
        print(f"\n📝 INSERT操作示例:")
        for op in usage['insert_operations'][:2]:
            print(f"  {op}")
    
    if usage['select_operations']:
        print(f"\n🔍 SELECT操作示例:")
        for op in usage['select_operations'][:2]:
            print(f"  {op}")
    
    if usage['join_operations']:
        print(f"\n🔗 JOIN操作示例:")
        for op in usage['join_operations'][:2]:
            print(f"  {op}")
    
    # 显示主要引用位置
    if usage['references']:
        print(f"\n📍 主要引用位置:")
        # 只显示前3个引用
        for ref in usage['references'][:3]:
            print(f"  {ref}")
        if len(usage['references']) > 3:
            print(f"  ... 还有 {len(usage['references']) - 3} 个引用")

def check_roles_table_usage():
    """特别检查roles表的使用情况（这个表有数据）"""
    print(f"\n" + "=" * 80)
    print("🔍 对比分析：roles表（有数据的表）的使用情况")
    print("-" * 50)
    
    usage = analyze_table_in_code('roles')
    display_usage_analysis('roles', usage)

def generate_final_recommendations():
    """生成最终建议"""
    print(f"\n" + "=" * 80)
    print("📋 最终建议总结")
    print("-" * 50)
    
    recommendations = {
        'definitely_keep': [
            'permissions - 权限系统核心表，在auth.py中被引用',
            'user_roles - 用户角色关联，在auth.py和routes.py中被引用',
            'attendance_corrections - 考勤修正功能，业务需求明确',
            'overtime_requests - 加班申请功能，业务需求明确'
        ],
        'probably_keep': [
            'department_rules - 部门规则关联，扩展功能',
            'user_groups - 用户分组，权限管理扩展',
            'activity_logs - 操作日志，审计功能',
            'approval_workflows - 审批工作流，高级功能',
            'approval_steps - 审批步骤，配套功能'
        ],
        'optional': [
            'permission_groups - 权限分组，可用roles表替代',
            'report_templates - 报表模板，可用固定报表替代',
            'rule_details - 规则详情，可在主表中存储'
        ]
    }
    
    print("🟢 强烈建议保留:")
    for item in recommendations['definitely_keep']:
        print(f"  • {item}")
    
    print("\n🟡 建议保留:")
    for item in recommendations['probably_keep']:
        print(f"  • {item}")
    
    print("\n🟠 可选保留:")
    for item in recommendations['optional']:
        print(f"  • {item}")
    
    print(f"\n💡 总体建议:")
    print("  1. 不建议删除任何空表，因为它们都是有价值的功能预留")
    print("  2. 优先实现permissions和user_roles的完整功能")
    print("  3. 根据业务需求逐步实现其他功能")
    print("  4. 空表不占用显著存储空间，保留它们有利于系统扩展")

if __name__ == "__main__":
    try:
        analyze_specific_table_usage()
        check_roles_table_usage()
        generate_final_recommendations()
        
    except Exception as e:
        print(f"分析过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
