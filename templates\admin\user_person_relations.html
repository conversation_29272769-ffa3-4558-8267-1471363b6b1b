{% extends "admin/base.html" %}

{% block title %}用户与人员关联管理{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">用户与人员关联管理</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/admin/">首页</a></li>
        <li class="breadcrumb-item active">用户与人员关联管理</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-link me-1"></i>
            创建新关联
        </div>
        <div class="card-body">
            <form id="relationForm" class="row g-3">
                <div class="col-md-5">
                    <label for="user_id" class="form-label">用户</label>
                    <select class="form-select" id="user_id" name="user_id" required>
                        <option value="">请选择用户</option>
                        {% for user in unrelated_users %}
                        <option value="{{ user.id }}">{{ user.username }} ({{ user.role }})</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-5">
                    <label for="person_id" class="form-label">人员</label>
                    <select class="form-select" id="person_id" name="person_id" required>
                        <option value="">请选择人员</option>
                        {% for person in unrelated_persons %}
                        <option value="{{ person.id }}">{{ person.name }} ({{ person.employee_id or '无工号' }}{% if person.department_name %}, {{ person.department_name }}{% endif %})</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">创建关联</button>
                </div>
            </form>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            现有关联列表
        </div>
        <div class="card-body">
            <table id="relationsTable" class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>用户名</th>
                        <th>人员姓名</th>
                        <th>工号</th>
                        <th>部门</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for relation in relations %}
                    <tr>
                        <td>{{ relation.id }}</td>
                        <td>{{ relation.username }}</td>
                        <td>{{ relation.person_name }}</td>
                        <td>{{ relation.employee_id or '无' }}</td>
                        <td>{{ relation.department_name or '无' }}</td>
                        <td>{{ relation.created_at }}</td>
                        <td>
                            <button class="btn btn-sm btn-danger delete-relation" 
                                    data-user-id="{{ relation.user_id }}" 
                                    data-person-id="{{ relation.person_id }}">
                                删除
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 确认删除模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除这个关联吗？此操作不可撤销。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 提示模态框 -->
<div class="modal fade" id="alertModal" tabindex="-1" aria-labelledby="alertModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="alertModalLabel">提示</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="alertModalBody">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">确定</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 初始化数据表格
        $('#relationsTable').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/zh.json',
            },
            order: [[0, 'desc']], // 按ID降序排序
            columnDefs: [
                { targets: 0, type: 'num' } // 确保ID列按数字排序
            ]
        });
        
        // 创建关联表单提交
        $('#relationForm').on('submit', function(e) {
            e.preventDefault();

            const userId = $('#user_id').val();
            const personId = $('#person_id').val();

            console.log('创建关联，用户ID:', userId, '人员ID:', personId);

            if (!userId || !personId) {
                showAlert('请选择用户和人员');
                return;
            }

            $.ajax({
                url: '/admin/api/user-person-relations/create',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    user_id: parseInt(userId),
                    person_id: parseInt(personId)
                }),
                success: function(response) {
                    console.log('创建响应:', response);

                    if (response.success) {
                        showAlert('关联创建成功', function() {
                            window.location.reload();
                        });
                    } else {
                        showAlert('创建失败: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.log('创建错误:', xhr.responseText);
                    showAlert('服务器错误，请稍后再试: ' + error);
                }
            });
        });
        
        // 删除关联 - 使用事件委托处理分页后的元素
        let deleteUserId, deletePersonId;

        $(document).on('click', '.delete-relation', function() {
            deleteUserId = $(this).data('user-id');
            deletePersonId = $(this).data('person-id');
            console.log('删除按钮点击，用户ID:', deleteUserId, '人员ID:', deletePersonId);

            // 使用Bootstrap 5的方式显示模态框
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        });
        
        $('#confirmDelete').on('click', function() {
            console.log('确认删除，用户ID:', deleteUserId, '人员ID:', deletePersonId);

            if (!deleteUserId || !deletePersonId) {
                showAlert('删除参数错误');
                return;
            }

            $.ajax({
                url: '/admin/api/user-person-relations/delete',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    user_id: parseInt(deleteUserId),
                    person_id: parseInt(deletePersonId)
                }),
                success: function(response) {
                    console.log('删除响应:', response);

                    // 使用Bootstrap 5的方式隐藏模态框
                    const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
                    if (deleteModal) {
                        deleteModal.hide();
                    }

                    if (response.success) {
                        showAlert('关联删除成功', function() {
                            window.location.reload();
                        });
                    } else {
                        showAlert('删除失败: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.log('删除错误:', xhr.responseText);

                    // 使用Bootstrap 5的方式隐藏模态框
                    const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
                    if (deleteModal) {
                        deleteModal.hide();
                    }

                    showAlert('服务器错误，请稍后再试: ' + error);
                }
            });
        });
        
        // 显示提示框
        function showAlert(message, callback) {
            $('#alertModalBody').text(message);
            const alertModal = new bootstrap.Modal(document.getElementById('alertModal'));
            
            if (callback) {
                $('#alertModal').on('hidden.bs.modal', callback);
            }
            
            alertModal.show();
        }
    });
</script>
{% endblock %}
