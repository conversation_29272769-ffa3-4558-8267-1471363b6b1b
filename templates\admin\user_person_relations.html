{% extends "admin/base.html" %}

{% block title %}用户与人员关联管理{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
    }
    .stats-card .card-body {
        padding: 1.5rem;
    }
    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    .relation-card {
        transition: all 0.3s ease;
        border-left: 4px solid #007bff;
    }
    .relation-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    .user-badge {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.8rem;
    }
    .person-badge {
        background: linear-gradient(45deg, #17a2b8, #6f42c1);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.8rem;
    }
    .alert-custom {
        border-left: 4px solid;
        border-radius: 0.5rem;
    }
    .alert-success-custom {
        border-left-color: #28a745;
        background-color: #d4edda;
        color: #155724;
    }
    .alert-error-custom {
        border-left-color: #dc3545;
        background-color: #f8d7da;
        color: #721c24;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- 页面标题 -->
    <div class="d-flex justify-content-between align-items-center mt-4 mb-4">
        <div>
            <h1 class="mb-0">用户与人员关联管理</h1>
            <p class="text-muted mb-0">管理系统用户账号与人员信息的关联关系</p>
        </div>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createRelationModal">
            <i class="fas fa-plus me-2"></i>创建新关联
        </button>
    </div>

    <!-- 面包屑导航 -->
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/admin/">首页</a></li>
        <li class="breadcrumb-item active">用户与人员关联管理</li>
    </ol>

    <!-- 消息提示 -->
    {% if request.query_params.get('success') %}
    <div class="alert alert-custom alert-success-custom alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>{{ request.query_params.get('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}
    {% if request.query_params.get('error') %}
    <div class="alert alert-custom alert-error-custom alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>{{ request.query_params.get('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    <!-- 统计信息卡片 -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card mb-4">
                <div class="card-body text-center">
                    <div class="stats-number">{{ total_relations }}</div>
                    <div class="stats-label">总关联数</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card mb-4">
                <div class="card-body text-center">
                    <div class="stats-number">{{ total_unrelated_users }}</div>
                    <div class="stats-label">未关联用户</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card mb-4">
                <div class="card-body text-center">
                    <div class="stats-number">{{ total_unrelated_persons }}</div>
                    <div class="stats-label">未关联人员</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card mb-4">
                <div class="card-body text-center">
                    <div class="stats-number">{{ relations|length }}</div>
                    <div class="stats-label">活跃关联</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 关联列表 -->
    <div class="card relation-card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-link me-2"></i>
                <strong>现有关联列表</strong>
                <span class="badge bg-primary ms-2">{{ relations|length }} 条记录</span>
            </div>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-secondary btn-sm" id="refreshBtn">
                    <i class="fas fa-sync-alt me-1"></i>刷新
                </button>
                <button type="button" class="btn btn-outline-info btn-sm" data-bs-toggle="collapse" data-bs-target="#filterPanel">
                    <i class="fas fa-filter me-1"></i>筛选
                </button>
            </div>
        </div>

        <!-- 筛选面板 -->
        <div class="collapse" id="filterPanel">
            <div class="card-body border-bottom bg-light">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">用户筛选</label>
                        <select class="form-select" id="userFilter">
                            <option value="">所有用户</option>
                            {% for user in all_users %}
                            <option value="{{ user.username }}">{{ user.username }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">人员筛选</label>
                        <select class="form-select" id="personFilter">
                            <option value="">所有人员</option>
                            {% for person in all_persons %}
                            <option value="{{ person.name }}">{{ person.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="button" class="btn btn-secondary me-2" id="clearFilters">
                            <i class="fas fa-times me-1"></i>清除筛选
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body">
            {% if relations %}
            <div class="table-responsive">
                <table id="relationsTable" class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th width="8%">ID</th>
                            <th width="20%">用户信息</th>
                            <th width="25%">人员信息</th>
                            <th width="15%">工号</th>
                            <th width="15%">部门</th>
                            <th width="12%">创建时间</th>
                            <th width="5%">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for relation in relations %}
                        <tr>
                            <td>
                                <span class="badge bg-secondary">#{{ relation.id }}</span>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-user me-2 text-success"></i>
                                    <div>
                                        <div class="fw-bold">{{ relation.username }}</div>
                                        <small class="text-muted">系统用户</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-id-card me-2 text-info"></i>
                                    <div>
                                        <div class="fw-bold">{{ relation.person_name }}</div>
                                        <small class="text-muted">员工信息</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if relation.employee_id %}
                                <span class="badge bg-info">{{ relation.employee_id }}</span>
                                {% else %}
                                <span class="text-muted">未设置</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if relation.department_name %}
                                <span class="badge bg-secondary">{{ relation.department_name }}</span>
                                {% else %}
                                <span class="text-muted">未分配</span>
                                {% endif %}
                            </td>
                            <td>
                                <small class="text-muted">{{ relation.created_at }}</small>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-outline-danger delete-relation"
                                        data-relation-id="{{ relation.id }}"
                                        data-username="{{ relation.username }}"
                                        data-person-name="{{ relation.person_name }}"
                                        title="删除关联">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-link fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无关联记录</h5>
                <p class="text-muted">点击上方"创建新关联"按钮开始创建用户与人员的关联关系</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createRelationModal">
                    <i class="fas fa-plus me-2"></i>创建第一个关联
                </button>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 创建关联模态框 -->
<div class="modal fade" id="createRelationModal" tabindex="-1" aria-labelledby="createRelationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="createRelationModalLabel">
                    <i class="fas fa-plus me-2"></i>创建用户与人员关联
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="createRelationForm" method="post" action="/admin/user-person-relations/create">
                <div class="modal-body">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <label for="user_id" class="form-label">
                                <i class="fas fa-user me-2 text-success"></i>选择用户
                            </label>
                            <select class="form-select" id="user_id" name="user_id" required>
                                <option value="">请选择用户账号</option>
                                {% for user in unrelated_users %}
                                <option value="{{ user.id }}">
                                    {{ user.username }}
                                    {% if user.role %}<span class="text-muted">({{ user.role }})</span>{% endif %}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="form-text">选择要关联的系统用户账号</div>
                        </div>
                        <div class="col-md-6">
                            <label for="person_id" class="form-label">
                                <i class="fas fa-id-card me-2 text-info"></i>选择人员
                            </label>
                            <select class="form-select" id="person_id" name="person_id" required>
                                <option value="">请选择人员信息</option>
                                {% for person in unrelated_persons %}
                                <option value="{{ person.id }}">
                                    {{ person.name }}
                                    {% if person.employee_id %} (工号: {{ person.employee_id }}){% endif %}
                                    {% if person.department_name %} - {{ person.department_name }}{% endif %}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="form-text">选择要关联的员工信息</div>
                        </div>
                    </div>

                    {% if not unrelated_users or not unrelated_persons %}
                    <div class="alert alert-warning mt-3">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {% if not unrelated_users and not unrelated_persons %}
                        当前没有可关联的用户和人员。所有用户和人员都已建立关联关系。
                        {% elif not unrelated_users %}
                        当前没有可关联的用户。所有用户都已建立关联关系。
                        {% else %}
                        当前没有可关联的人员。所有人员都已建立关联关系。
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>取消
                    </button>
                    <button type="submit" class="btn btn-primary" {% if not unrelated_users or not unrelated_persons %}disabled{% endif %}>
                        <i class="fas fa-link me-2"></i>创建关联
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 确认删除模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>确认删除关联
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <i class="fas fa-unlink fa-3x text-danger mb-3"></i>
                    <h6>您确定要删除以下关联吗？</h6>
                </div>
                <div class="row">
                    <div class="col-6 text-center">
                        <div class="border rounded p-3 bg-light">
                            <i class="fas fa-user text-success mb-2"></i>
                            <div class="fw-bold" id="deleteUsername">用户名</div>
                            <small class="text-muted">系统用户</small>
                        </div>
                    </div>
                    <div class="col-6 text-center">
                        <div class="border rounded p-3 bg-light">
                            <i class="fas fa-id-card text-info mb-2"></i>
                            <div class="fw-bold" id="deletePersonName">人员姓名</div>
                            <small class="text-muted">员工信息</small>
                        </div>
                    </div>
                </div>
                <div class="alert alert-warning mt-3 mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>注意：</strong>删除关联后，该用户将无法访问对应的人员考勤数据，此操作不可撤销。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>取消
                </button>
                <button type="button" class="btn btn-danger" id="confirmDelete">
                    <i class="fas fa-trash me-2"></i>确认删除
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 提示模态框 -->
<div class="modal fade" id="alertModal" tabindex="-1" aria-labelledby="alertModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="alertModalLabel">
                    <i class="fas fa-info-circle me-2"></i>系统提示
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="alertModalBody">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                    <i class="fas fa-check me-2"></i>确定
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // 初始化数据表格
        let table = null;
        if ($('#relationsTable').length) {
            table = $('#relationsTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/zh.json',
                },
                order: [[0, 'desc']], // 按ID降序排序
                columnDefs: [
                    { targets: 0, type: 'num' }, // 确保ID列按数字排序
                    { targets: -1, orderable: false } // 操作列不可排序
                ],
                pageLength: 10,
                responsive: true,
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip'
            });
        }

        // 刷新按钮
        $('#refreshBtn').on('click', function() {
            window.location.reload();
        });

        // 筛选功能
        $('#userFilter, #personFilter').on('change', function() {
            if (table) {
                const userFilter = $('#userFilter').val();
                const personFilter = $('#personFilter').val();

                // 重置搜索
                table.search('').columns().search('').draw();

                // 应用筛选
                if (userFilter) {
                    table.column(1).search(userFilter);
                }
                if (personFilter) {
                    table.column(2).search(personFilter);
                }

                table.draw();
            }
        });

        // 清除筛选
        $('#clearFilters').on('click', function() {
            $('#userFilter, #personFilter').val('');
            if (table) {
                table.search('').columns().search('').draw();
            }
        });

        // 创建关联表单提交
        $('#createRelationForm').on('submit', function(e) {
            const userId = $('#user_id').val();
            const personId = $('#person_id').val();

            if (!userId || !personId) {
                e.preventDefault();
                showAlert('请选择用户和人员', 'warning');
                return false;
            }

            // 显示加载状态
            const submitBtn = $(this).find('button[type="submit"]');
            const originalText = submitBtn.html();
            submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>创建中...').prop('disabled', true);

            // 表单会自动提交，这里只是添加加载状态
            setTimeout(() => {
                submitBtn.html(originalText).prop('disabled', false);
            }, 3000);
        });

        // 删除关联 - 使用事件委托处理分页后的元素
        let deleteRelationId, deleteUsername, deletePersonName;

        $(document).on('click', '.delete-relation', function() {
            deleteRelationId = $(this).data('relation-id');
            deleteUsername = $(this).data('username');
            deletePersonName = $(this).data('person-name');

            // 更新模态框内容
            $('#deleteUsername').text(deleteUsername);
            $('#deletePersonName').text(deletePersonName);

            // 显示删除确认模态框
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        });

        // 确认删除
        $('#confirmDelete').on('click', function() {
            if (!deleteRelationId) {
                showAlert('删除参数错误', 'error');
                return;
            }

            const btn = $(this);
            const originalText = btn.html();
            btn.html('<i class="fas fa-spinner fa-spin me-2"></i>删除中...').prop('disabled', true);

            $.ajax({
                url: `/admin/api/user-person-relations/${deleteRelationId}`,
                type: 'DELETE',
                success: function(response) {
                    // 隐藏模态框
                    const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
                    if (deleteModal) {
                        deleteModal.hide();
                    }

                    if (response.success) {
                        showAlert('关联删除成功', 'success', function() {
                            window.location.reload();
                        });
                    } else {
                        showAlert('删除失败: ' + response.message, 'error');
                    }
                },
                error: function(xhr, status, error) {
                    // 隐藏模态框
                    const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
                    if (deleteModal) {
                        deleteModal.hide();
                    }

                    let errorMessage = '服务器错误，请稍后再试';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    showAlert(errorMessage, 'error');
                },
                complete: function() {
                    btn.html(originalText).prop('disabled', false);
                }
            });
        });

        // 显示提示框
        function showAlert(message, type = 'info', callback) {
            $('#alertModalBody').html(`
                <div class="text-center">
                    <i class="fas ${getAlertIcon(type)} fa-2x text-${getAlertColor(type)} mb-3"></i>
                    <p class="mb-0">${message}</p>
                </div>
            `);

            const alertModal = new bootstrap.Modal(document.getElementById('alertModal'));

            if (callback) {
                $('#alertModal').off('hidden.bs.modal').on('hidden.bs.modal', callback);
            }

            alertModal.show();
        }

        function getAlertIcon(type) {
            switch(type) {
                case 'success': return 'fa-check-circle';
                case 'error': return 'fa-exclamation-circle';
                case 'warning': return 'fa-exclamation-triangle';
                default: return 'fa-info-circle';
            }
        }

        function getAlertColor(type) {
            switch(type) {
                case 'success': return 'success';
                case 'error': return 'danger';
                case 'warning': return 'warning';
                default: return 'info';
            }
        }

        // 自动隐藏成功/错误消息
        setTimeout(function() {
            $('.alert-dismissible').fadeOut();
        }, 5000);
    });
</script>
{% endblock %}
