#!/usr/bin/env python3
"""
简单获取表结构
"""
import sqlite3

DATABASE = "face_recognition.db"

def get_simple_table_info():
    """简单获取表信息"""
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()
    
    tables = ['announcements', 'leave_approval_flows', 'leave_approval_logs', 'roles']
    
    for table in tables:
        print(f"\n=== {table} ===")
        try:
            c.execute(f"PRAGMA table_info({table})")
            columns = c.fetchall()
            
            print("字段信息:")
            for col in columns:
                print(f"  {col}")
                
        except Exception as e:
            print(f"错误: {e}")
    
    conn.close()

if __name__ == "__main__":
    get_simple_table_info()
