## 人脸识别界面

```
无法识别人脸不停显示未匹配到人脸（同时在系统设置中请添加一个调试开关来控制开关对考勤时间的限制 我需要调试用）
```

## Admin

http://127.0.0.1:8001/admin/

仪表盘

```
ALL PASS
```

用户管理

```
ALL PASS
```

部门管理 *** 

```
表格中的删除按钮不可用（第二页的我自己新增的部门不可用http://127.0.0.1:8001/admin/departments） 
```

人员管理 ***

```
表格中的删除按钮不可用（第一页的可以删除第二页的就是不行http://127.0.0.1:8001/admin/persons）
考勤规则管理中删除按钮不可用（http://127.0.0.1:8001/admin/persons/1(这里可能是其他的数字代表不同的人员)/rules）
```

考勤管理

```
表格中的删除按钮不可用（第二页的用不了http://127.0.0.1:8001/admin/attendance）
```

考勤规则

```

```

请假管理

```
http://127.0.0.1:8001/admin/leave
请假类型管理按钮点击后会显示{"detail":[{"loc":["path","leave_id"],"msg":"value is not a valid integer","type":"type_error.integer"}]}
请你将请假类型管理这个按钮删除或整合到上面请假申请筛选中并把它修好，如果修不好就删了它
```

统计报表

```
ALL PASS
```

通知公告

```
通知公告中新增公告时公告类型只存在普通、重要、紧急三个类型，但是现在数据库中存在的公告中公告类型却出现了培训通知、假期通知、系统通知等类型，我希望你能将数据库中已有通知类型与新增公告功能中支持的通知类型统一起来。（你可以修改数据库中已有的公告数据中的公告类型为普通、重要、紧急三个类型中的其中一个http://127.0.0.1:8001/admin/announcements）
```

用户人员关联 ***

```
进行用户与人员的关联后在下方的现有关联列表中没有出现新的关联关系，并且使用该用户名登录后也显示没有关联到人员信息。http://127.0.0.1:8001/admin/user-person-relations
```

系统设置

```
我想要你重写一下用户人员关联页面(http://127.0.0.1:8001/admin/user-person-relations)的前后端，我想要通过这个页面将我的用户(用于登陆系统的账号密码)与人员（记录考勤系统用户的个人信息与关联考勤数据等等内容）关联起来，我希望这个页面拥有以下功能：
1、创建新的用户与人员的关联关系
2、删除现有的用户与人员的关联关系
3、显示现有的用户与人员的关联关系
并且要保持现有的数据库中的表关系不变
```

## User

仪表盘

```
ALL DONE
```

通知公告

```
通知公告页面(http://127.0.0.1:8001/user/announcements)进入后会弹窗提醒DataTables warning: table id=DataTables_Table_0 - Cannot reinitialise DataTable. For more information about this error, please see http://datatables.net/tn/3
请你解决这个问题(解决不了可以不解决，关闭弹窗后不影响实际使用)
```

考勤记录

```
考勤记录界面(http://127.0.0.1:8001/user/attendance)也是在进行考勤数据查询是会弹窗DataTables warning: table id=DataTables_Table_0 - Cannot reinitialise DataTable. For more information about this error, please see http://datatables.net/tn/3

并且查询出来的记录类型全部都是未知，请你查出来是数据库问题还是前端显示问题
```

请假管理

个人资料

DataTables warning: table id=DataTables_Table_0 - Cannot reinitialise DataTable. For more information about this error, please see http://datatables.net/tn/3

```
eyJhbGciOiJIUzI1NiIsImtpZCI6IlV6SXJWd1h0dnprLVRvdzlLZWstc0M1akptWXBvX1VaVkxUZlpnMDRlOFUiLCJ0eXAiOiJKV1QifQ.eyJzdWIiOiJnb29nbGUtb2F1dGgyfDEwMTE4NjEzMzE3ODQ1MzkxMzc4OCIsInNjb3BlIjoib3BlbmlkIG9mZmxpbmVfYWNjZXNzIiwiaXNzIjoiYXBpX2tleV9pc3N1ZXIiLCJhdWQiOlsiaHR0cHM6Ly9uZWJpdXMtaW5mZXJlbmNlLmV1LmF1dGgwLmNvbS9hcGkvdjIvIl0sImV4cCI6MTkwNjU0NzgwMCwidXVpZCI6IjFlNGMyNDdkLTdiNWItNDI4Ny1hZjhhLTU0NWY2M2E0NjgzZSIsIm5hbWUiOiJpbWFnZSIsImV4cGlyZXNfYXQiOiIyMDMwLTA2LTAxVDEyOjM2OjQwKzAwMDAifQ.gPkqCN4LXIAEEAreMgQ8tk959K5RnFEEg1dnGvfBEhU
```

