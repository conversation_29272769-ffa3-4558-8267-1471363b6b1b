#!/usr/bin/env python3
"""
检查attendance_backup表的使用情况
"""
import sqlite3
import os
import re

DATABASE = "face_recognition.db"

def check_attendance_backup_table():
    """检查attendance_backup表的详细情况"""
    print("attendance_backup表使用情况分析")
    print("=" * 60)
    
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    
    try:
        # 1. 检查表是否存在
        c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='attendance_backup'")
        table_exists = c.fetchone()
        
        if not table_exists:
            print("❌ attendance_backup表不存在")
            return
        
        print("✅ attendance_backup表存在")
        
        # 2. 检查表结构
        c.execute("PRAGMA table_info(attendance_backup)")
        columns = c.fetchall()
        
        print(f"\n📋 表结构:")
        print("  字段名          | 类型      | 非空 | 默认值")
        print("  " + "-" * 45)
        for col in columns:
            name = col[1]
            type_name = col[2]
            not_null = "是" if col[3] else "否"
            default = col[4] if col[4] is not None else ""
            print(f"  {name:<15} | {type_name:<8} | {not_null:<3} | {default}")
        
        # 3. 检查数据量
        c.execute("SELECT COUNT(*) as count FROM attendance_backup")
        count = c.fetchone()['count']
        print(f"\n📊 数据量: {count} 条记录")
        
        # 4. 检查数据创建时间
        if count > 0:
            c.execute("SELECT MIN(timestamp) as min_time, MAX(timestamp) as max_time FROM attendance_backup")
            time_range = c.fetchone()
            print(f"📅 数据时间范围: {time_range['min_time']} 到 {time_range['max_time']}")
            
            # 5. 检查数据分布
            c.execute("SELECT check_type, COUNT(*) as count FROM attendance_backup GROUP BY check_type")
            type_distribution = c.fetchall()
            print(f"\n📈 数据类型分布:")
            for row in type_distribution:
                print(f"  - {row['check_type']}: {row['count']} 条")
        
        # 6. 检查表创建时间（通过查看最近的数据库操作）
        print(f"\n🕒 表创建信息:")
        print("  这个表是在考勤记录类型转换过程中创建的备份表")
        
    except Exception as e:
        print(f"检查表信息时出错: {str(e)}")
    finally:
        conn.close()

def search_backup_table_usage():
    """搜索代码中对attendance_backup表的使用"""
    print(f"\n🔍 代码使用情况分析:")
    print("-" * 40)
    
    table_name = "attendance_backup"
    usage_found = False
    
    # 搜索的文件和目录
    search_targets = [
        'admin',
        'user', 
        'templates',
        'main.py',
        '.',  # 当前目录的py文件
    ]
    
    # SQL操作模式
    sql_patterns = [
        rf'CREATE\s+TABLE.*{table_name}',
        rf'INSERT\s+INTO\s+{table_name}',
        rf'SELECT.*FROM\s+{table_name}',
        rf'UPDATE\s+{table_name}',
        rf'DELETE\s+FROM\s+{table_name}',
        rf'DROP\s+TABLE\s+{table_name}',
        table_name
    ]
    
    references = []
    
    for target in search_targets:
        if os.path.isfile(target):
            files_to_check = [target]
        elif os.path.isdir(target):
            files_to_check = []
            for root, _, files in os.walk(target):
                if '__pycache__' in root:
                    continue
                for file in files:
                    if file.endswith(('.py', '.html', '.js', '.sql')):
                        files_to_check.append(os.path.join(root, file))
        else:
            continue
        
        for file_path in files_to_check:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    
                    if table_name in content:
                        usage_found = True
                        
                        # 查找包含表名的行
                        lines = content.split('\n')
                        for i, line in enumerate(lines, 1):
                            if table_name in line and line.strip():
                                references.append(f"{file_path}:{i}: {line.strip()}")
                        
                        # 检查SQL操作
                        for pattern in sql_patterns[:-1]:  # 排除最后一个通用模式
                            matches = re.findall(pattern, content, re.IGNORECASE)
                            if matches:
                                for match in matches:
                                    print(f"  🔧 SQL操作: {file_path} - {match}")
            
            except Exception:
                pass
    
    if usage_found:
        print(f"  ✅ 在代码中被引用")
        print(f"  📍 引用位置:")
        for ref in references[:10]:  # 只显示前10个
            print(f"    {ref}")
        if len(references) > 10:
            print(f"    ... 还有 {len(references) - 10} 个引用")
    else:
        print(f"  ❌ 在代码中未被引用")
    
    return usage_found

def analyze_backup_purpose():
    """分析备份表的用途"""
    print(f"\n🎯 用途分析:")
    print("-" * 40)
    
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    
    try:
        # 检查attendance_backup和attendance表的数据对比
        c.execute("SELECT COUNT(*) as count FROM attendance_backup")
        backup_count = c.fetchone()['count']
        
        c.execute("SELECT COUNT(*) as count FROM attendance WHERE check_type IN ('check_in', 'check_out')")
        current_count = c.fetchone()['count']
        
        c.execute("SELECT COUNT(*) as count FROM attendance WHERE check_type = 'face'")
        face_count = c.fetchone()['count']
        
        print(f"  📊 数据对比:")
        print(f"    - attendance_backup表: {backup_count} 条记录")
        print(f"    - attendance表(check_in/out): {current_count} 条记录")
        print(f"    - attendance表(face类型): {face_count} 条记录")
        
        if backup_count > 0 and face_count == 0:
            print(f"\n  💡 分析结论:")
            print(f"    这是在考勤记录类型转换时创建的备份表")
            print(f"    备份了原始的'face'类型记录，现在这些记录已被转换为'check_in'/'check_out'")
            print(f"    备份表的作用是为了在转换出错时能够恢复原始数据")
        
        # 检查备份表中的数据类型
        if backup_count > 0:
            c.execute("SELECT DISTINCT check_type FROM attendance_backup")
            backup_types = c.fetchall()
            print(f"\n  📋 备份表中的记录类型:")
            for row in backup_types:
                print(f"    - {row['check_type']}")
    
    except Exception as e:
        print(f"分析时出错: {str(e)}")
    finally:
        conn.close()

def generate_backup_recommendations():
    """生成关于备份表的建议"""
    print(f"\n💡 建议:")
    print("-" * 40)
    
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()
    
    try:
        c.execute("SELECT COUNT(*) FROM attendance_backup")
        backup_count = c.fetchone()[0]
        
        c.execute("SELECT COUNT(*) FROM attendance WHERE check_type = 'face'")
        face_count = c.fetchone()[0]
        
        if backup_count > 0 and face_count == 0:
            print("  🟢 当前状态: 转换已成功完成")
            print("  📋 备份表用途: 保存转换前的原始数据")
            print()
            print("  🔧 处理建议:")
            print("    1. 短期保留 (推荐): 保留30-60天作为安全缓冲")
            print("    2. 长期保留: 如果需要历史数据审计")
            print("    3. 删除备份: 如果确认转换无问题且不需要回滚")
            print()
            print("  ⚠️  删除前确认:")
            print("    - 转换后的考勤数据工作正常")
            print("    - 用户对新的考勤记录显示满意")
            print("    - 不需要回滚到原始的'face'类型记录")
            print()
            print("  🗑️  删除命令 (可选):")
            print("    DROP TABLE attendance_backup;")
        else:
            print("  ℹ️  状态未明确，建议保留备份表")
    
    except Exception as e:
        print(f"生成建议时出错: {str(e)}")
    finally:
        conn.close()

if __name__ == "__main__":
    try:
        check_attendance_backup_table()
        usage_found = search_backup_table_usage()
        analyze_backup_purpose()
        generate_backup_recommendations()
        
        print(f"\n" + "=" * 60)
        print("分析完成！")
        
    except Exception as e:
        print(f"分析过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
