#!/usr/bin/env python3
import sqlite3

DATABASE = "face_recognition.db"

def check_announcement_types():
    """检查当前数据库中的公告类型"""
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()
    
    try:
        # 查看所有公告的详细信息
        c.execute("""
            SELECT id, title, type, priority, status, created_at 
            FROM announcements 
            ORDER BY created_at DESC
        """)
        announcements = c.fetchall()
        
        print("当前数据库中的所有公告:")
        print("-" * 80)
        for announcement in announcements:
            id, title, type, priority, status, created_at = announcement
            type_name = {'normal': '普通', 'important': '重要', 'urgent': '紧急'}.get(type, type)
            print(f"ID: {id}")
            print(f"标题: {title}")
            print(f"类型: {type_name} ({type})")
            print(f"优先级: {priority}")
            print(f"状态: {status}")
            print(f"创建时间: {created_at}")
            print("-" * 80)
        
        # 统计各类型数量
        c.execute("SELECT type, COUNT(*) FROM announcements GROUP BY type")
        type_counts = c.fetchall()
        
        print("\n公告类型统计:")
        for type, count in type_counts:
            type_name = {'normal': '普通', 'important': '重要', 'urgent': '紧急'}.get(type, type)
            print(f"  - {type_name} ({type}): {count} 条")
            
    except Exception as e:
        print(f"检查公告类型时出错: {str(e)}")
    finally:
        conn.close()

if __name__ == "__main__":
    check_announcement_types()
