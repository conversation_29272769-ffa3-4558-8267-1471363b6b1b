# 人脸识别考勤系统 - 项目文件结构详细说明文档

## 第2部分：static文件夹与user文件夹

---

## static文件夹详细说明

static文件夹包含了项目的静态资源文件，主要是前端JavaScript代码，负责处理用户交互和前端逻辑。

### JavaScript文件

#### `static/js/auth.js`
**作用**: 用户认证相关的前端逻辑
- **登录表单处理**: 处理用户登录表单的提交和验证
- **密码验证**: 前端密码格式验证
- **登录状态管理**: 管理用户登录状态的前端逻辑
- **错误提示**: 显示登录失败的错误信息
- **重定向处理**: 登录成功后的页面跳转

**主要功能**:
```javascript
- 表单验证和提交
- AJAX登录请求
- 错误信息显示
- 登录状态检查
- 自动跳转逻辑
```

#### `static/js/face_recognition.js`
**作用**: 人脸识别功能的前端实现
- **摄像头调用**: 调用用户设备摄像头
- **人脸检测**: 实时检测摄像头中的人脸
- **图像捕获**: 捕获人脸图像并处理
- **识别请求**: 向后端发送人脸识别请求
- **结果显示**: 显示识别结果和考勤状态

**核心功能**:
```javascript
- navigator.mediaDevices.getUserMedia(): 获取摄像头权限
- 人脸框绘制和定位
- 图像数据处理和编码
- WebSocket或AJAX通信
- 实时状态更新
```

**技术特点**:
- 使用WebRTC技术访问摄像头
- Canvas绘制人脸检测框
- Base64图像编码传输
- 实时性能优化

#### `static/js/face_register.js`
**作用**: 人脸注册功能的前端实现
- **人脸采集**: 采集用户多角度人脸图像
- **图像质量检测**: 检测图像质量是否符合要求
- **注册流程控制**: 控制人脸注册的步骤流程
- **数据上传**: 将采集的人脸数据上传到服务器
- **注册结果反馈**: 显示注册成功或失败的结果

**主要流程**:
```javascript
1. 摄像头初始化
2. 人脸检测和定位
3. 图像质量评估
4. 多张图像采集
5. 数据打包上传
6. 注册结果处理
```

**技术实现**:
- 多帧图像采集
- 图像质量评分
- 批量数据上传
- 进度条显示
- 错误重试机制

---

## user文件夹详细说明

user文件夹包含了普通用户端的后端逻辑，负责处理用户的日常操作功能。

### 核心配置文件

#### `user/__init__.py`
**作用**: Python包初始化文件
- 将user目录标识为Python包
- 导入和暴露user模块的主要组件
- 定义用户模块的公共接口
- 初始化用户模块的配置

### 认证管理

#### `user/auth.py`
**作用**: 用户端认证和权限管理
- **用户登录验证**: 验证普通用户的登录凭据
- **会话管理**: 管理用户登录状态和会话
- **权限检查**: 验证用户访问权限
- **用户信息获取**: 获取当前登录用户的信息

**主要功能**:
```python
- get_current_user(): 获取当前登录用户
- verify_user_credentials(): 验证用户凭据
- user_required(): 用户权限装饰器
- check_user_permissions(): 检查用户权限
```

**与admin/auth.py的区别**:
- 面向普通用户而非管理员
- 权限级别较低
- 功能相对简化
- 安全要求适中

### 主要路由

#### `user/routes.py`
**作用**: 用户端主要路由定义
- **用户首页**: 用户登录后的主页面
- **个人信息**: 用户个人资料查看和编辑
- **考勤查询**: 用户查看自己的考勤记录
- **通知公告**: 查看系统通知和公告
- **个人统计**: 个人考勤统计和分析

**主要路由**:
```python
- /user/: 用户首页
- /user/profile: 个人信息页面
- /user/attendance: 考勤记录查询
- /user/announcements: 通知公告
- /user/statistics: 个人统计
```

**功能特点**:
- 只能访问与自己相关的数据
- 提供只读或有限的编辑功能
- 注重用户体验和界面友好性
- 数据安全和隐私保护

### 请假管理

#### `user/leave_routes.py`
**作用**: 用户端请假管理路由
- **请假申请**: 用户提交请假申请
- **请假记录查询**: 查看自己的请假历史
- **申请状态跟踪**: 跟踪请假申请的审批状态
- **请假统计**: 个人请假统计信息

**主要功能**:
```python
- /user/leave/request: 请假申请页面
- /user/leave/records: 请假记录查询
- /user/leave/detail/{id}: 请假详情查看
- 请假申请的提交和编辑
```

**业务流程**:
1. 用户填写请假申请表单
2. 系统验证请假信息
3. 提交申请到审批流程
4. 用户可查看申请状态
5. 接收审批结果通知

**数据权限**:
- 只能查看和操作自己的请假记录
- 不能查看其他用户的请假信息
- 不能直接修改审批状态
- 遵循数据隔离原则

### 模块特点对比

#### user模块 vs admin模块

| 特性 | user模块 | admin模块 |
|------|----------|-----------|
| **用户权限** | 普通用户 | 管理员 |
| **功能范围** | 个人相关 | 全系统管理 |
| **数据访问** | 受限访问 | 全量访问 |
| **操作权限** | 查看+有限编辑 | 完全控制 |
| **安全级别** | 中等 | 高 |
| **界面复杂度** | 简洁 | 复杂 |

### 技术架构

#### 设计模式
- **MVC模式**: 分离业务逻辑、数据和视图
- **RESTful API**: 标准的API设计
- **权限控制**: 基于角色的访问控制
- **数据隔离**: 确保用户只能访问自己的数据

#### 安全机制
- **身份验证**: 确保用户身份真实性
- **权限验证**: 检查用户操作权限
- **数据过滤**: 过滤敏感数据
- **输入验证**: 验证用户输入数据

### 文件职责总结

**static文件夹**:
- 提供前端交互逻辑
- 处理人脸识别和注册
- 管理用户界面行为
- 优化用户体验

**user文件夹**:
- 实现用户端业务逻辑
- 提供用户相关API接口
- 管理用户权限和数据安全
- 支持用户日常操作需求

这两个模块共同构成了系统的用户端功能，为普通用户提供了完整的考勤管理体验。
