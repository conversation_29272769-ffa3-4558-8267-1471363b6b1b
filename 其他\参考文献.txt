区块链存证（IBM），边缘计算（终端部署轻量模型）；提升安全、响应速度。
1 葛旭伟.区块链通用数据存证服务平台的设计与实现[D].南昌大学,2022.DOI:10.27232/d.cnki.gnchu.2022.001870.


采用B/S架构，有利于前端与后端技术的解耦、系统的跨平台、系统的稳定性，并支持面向互联网的协同办公。
2 王志涛.基于B/S模式的项目管理信息系统开发与设计[J].办公自动化,2024,29(24):84-86.

在信息技术迅速发展的今天，企业管理的效率和精准度愈发依赖于智能技术的广
泛应用。
3 王燕.智能考勤系统在提升企业管理效率中的应用研究[J].经营管理者,2025,(03):79-81.

Python语言是一种面向对象的计算机程序设计语言,其编程语言灵活,功能强大,最大的特点是代码简练整齐,优雅干净,
4 葛书荣. 基于Python语言编程特点及应用之探讨[J]. 网络安全技术与应用,2021(10):37-38. DOI:10.3969/j.issn.1009-6833.2021.10.022.

Python丰富的开发生态提供了多种多样的第三方库,极大地提高了开发者的开发效率和质量
5 沈阚,黄凯锋,陈碧欢,等. 基于静态分析的Python第三方库API兼容性问题检测方法[J]. 软件学报,2025,36(4):1435-1460. DOI:10.13328/j.cnki.jos.007224.


SQLite作为轻量级嵌入式数据库，具备低系统开销和实时性强的特点，适用于移动设备数据存储。
6 彭艳. 基于嵌入式数据库SQLite的智能导游系统[J]. 计算机系统应用,2011,20(4):254-256. DOI:10.3969/j.issn.1003-3254.2011.04.059.


结果表明,结合Python灵活性 、SQLite轻量级特性的系统,在处理大量文件时表现出
显著高效率与稳定性,可充分满足现代文件管理需求。
7 张正平. 基于Python+SQLite的文件整理软件系统设计与实现 [J]. 信息与电脑, 2024, 36(5): 74-76.

FastAPI 是一个现代、快速（高性能）的 Web 框架，用于基于标准 Python 类型提示使用 Python 构建 API。
8 SEBASTIÁN RAMÍREZ. FastAPI[EB/OL]. (2023-05-20)[2025-05-19]. https://fastapi.tiangolo.com/

Bootstrap 作为一种流行的前端开发框架，提供了丰富的组件、灵活的布局和强大的定制能力，为响应式网站的开发提供了强大的支持。Bootstrap 框架具有易于使用、快速开发和跨平台兼容等优点，使得开发者能够更加高效地构建出美观、易用且具有良好用户体验的网站。
9 张曰花.Bootstrap响应式设计在甜点网站中的应用研究[J].现代信息科技,2024,8(15):88-92+98.DOI:10.19850/j.cnki.2096-4706.2024.15.019.

Bootstrap响应式设计技术的出现，顺应了互联网发展的需求，解决了用户跨终端浏览网页的问题，同时也为网页设计人员简化了设计流程，极大地提高了设计人员的工作效率。
10 庄丽君.Bootstrap响应式Web设计应用研究——以玩具商城网站设计为例[J].现代信息科技,2023,7(22):96-99.DOI:10.19850/j.cnki.2096-4706.2023.22.021.

李远.人脸识别课堂考勤系统的研究与实现[D].浙江工业大学,2020.DOI:10.27463/d.cnki.gzgyu.2020.000104.
曹光辉.基于人脸识别技术的考勤签到系统[J].电脑知识与技术,2020.
张其帆.基于人脸识别的课堂考勤系统的设计与实现[D].重庆师范大学,2019.
李雄,文开福,钟小明等.基于深度学习的人脸识别考勤管理系统开发[J].实验室研究与探索,2019.
王治强,孙晓东,杨永等.人脸识别算法在考勤系统的应用[J].计算机系统应用,2021.

人脸考勤系统

人脸识别考勤界面

用户界面
仪表盘 通知公告 考勤记录 请假管理 个人资料

管理员界面
仪表盘 用户管理 部门管理 人员管理 考勤管理 考勤规则 请假管理 统计报表 通知公告 用户人员关联管理 系统设置