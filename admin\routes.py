#!/usr/bin/env python3
import sqlite3
import hashlib
import io
from datetime import datetime, timedelta
from typing import Optional, List

from fastapi import APIRouter, Request, Depends, HTTPException, Form, Query, File, UploadFile
from fastapi.templating import Jinja2Templates
from fastapi.security import HTTPBasicCredentials, HTTPBasic
from fastapi.responses import RedirectResponse, StreamingResponse, JSONResponse

try:
    import xlsxwriter
except ImportError:
    # 如果没有安装xlsxwriter，使用其他方式导出Excel
    xlsxwriter = None

from .db_init import DATABASE, verify_password # 假设verify_password在db_init中
# 稍后可能需要导入主要的安全依赖项

router = APIRouter(
    prefix="/admin",
    tags=["admin"],
    # dependencies=[Depends(authenticate_user)], # 稍后添加身份验证
    responses={404: {"description": "Not found"}},
)

templates = Jinja2Templates(directory="templates")

# 身份验证占位符 - 稍后替换为实际的依赖项
async def get_current_admin_user(credentials: HTTPBasicCredentials = Depends(HTTPBasic())):
    # 验证管理员身份
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()

    # 首先检查用户是否存在
    c.execute("SELECT id, password_hash FROM users WHERE username=?", (credentials.username,))
    user_result = c.fetchone()

    if not user_result or not verify_password(credentials.password, user_result[1]):
        conn.close()
        raise HTTPException(
            status_code=401,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Basic realm=\"Admin Area\""}
        )

    # 检查用户是否有管理员角色
    user_id = user_result[0]

    # 首先检查role列
    c.execute("SELECT role FROM users WHERE id=?", (user_id,))
    role_result = c.fetchone()

    # 如果有role列且值为admin，则允许访问
    if role_result and role_result[0] == 'admin':
        conn.close()
        return credentials.username

    # 否则检查user_roles表
    c.execute("""
        SELECT r.permissions FROM user_roles ur
        JOIN roles r ON ur.role_id = r.id
        WHERE ur.user_id = ?
    """, (user_id,))

    role_permissions = c.fetchall()
    conn.close()

    # 检查是否有管理员权限
    for perm in role_permissions:
        try:
            if perm[0]:
                permissions = eval(perm[0].replace('true', 'True').replace('false', 'False'))
                if permissions.get('all') or permissions.get('admin'):
                    return credentials.username
        except:
            pass

    if not result or not verify_password(credentials.password, result[0]):
        raise HTTPException(
            status_code=401,
            detail="用户名或密码错误，或者不是管理员",
            headers={"WWW-Authenticate": "Basic realm=\"Admin Area\""},
        )
    return credentials.username

@router.get("/")
async def admin_dashboard(request: Request, username: str = Depends(get_current_admin_user)):
    """
    管理员仪表板首页。
    """
    # 稍后替换为实际的仪表板模板
    # 现在只返回一个简单的响应或重定向到特定部分
    # 我们先以用户管理视图为目标
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    c.execute("SELECT id, username, role, email, created_at, last_login FROM users ORDER BY id")
    users = c.fetchall()
    conn.close()
    # 假设admin/dashboard.html或类似文件存在或将被创建
    # 需要创建templates/admin目录和文件
    return templates.TemplateResponse("admin/users.html", {
        "request": request,
        "current_tab": "users",
        "users": users,
        "logged_in_user": username
    })

# 在这里添加更多路由，用于用户、部门、班次、规则、报表等
# 示例：用户管理路由
@router.get("/users")
async def manage_users(request: Request, username: str = Depends(get_current_admin_user)):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    c.execute("SELECT id, username, role, email, created_at, last_login FROM users ORDER BY id")
    users = c.fetchall()
    conn.close()
    # 需要创建templates/admin/users.html
    return templates.TemplateResponse("admin/users.html", {
        "request": request,
        "current_tab": "users",
        "users": users,
        "logged_in_user": username
    })

# 添加用于创建、更新、删除用户的路由...
@router.get("/users/new")
async def new_user_form(request: Request, username: str = Depends(get_current_admin_user)):
    return templates.TemplateResponse("admin/user_form.html", {
        "request": request,
        "current_tab": "users",
        "user": None,
        "logged_in_user": username
    })

@router.post("/users")
async def create_user(
    request: Request,
    username: str = Depends(get_current_admin_user),
    form_username: str = Form(...),
    password: str = Form(...),
    email: str = Form(None),
    role: str = Form("user")
):
    conn = sqlite3.connect(DATABASE)
    try:
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        c = conn.cursor()
        c.execute("""
            INSERT INTO users (username, password_hash, email, role)
            VALUES (?, ?, ?, ?)
        """, (form_username, password_hash, email, role))
        conn.commit()
        return RedirectResponse(url="/admin/users", status_code=303)
    except sqlite3.IntegrityError:
        conn.rollback()
        return templates.TemplateResponse("admin/user_form.html", {
            "request": request,
            "current_tab": "users",
            "user": None,
            "error": "用户名已存在",
            "logged_in_user": username
        })
    finally:
        conn.close()

@router.get("/attendance/export")
async def export_attendance(
    request: Request,
    username: str = Depends(get_current_admin_user),
    start_date: str = Query(...),
    end_date: str = Query(...),
    dept_id: int = Query(None)
):
    conn = sqlite3.connect(DATABASE)
    try:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()

        query = """
            SELECT a.date, p.name as person_name, d.name as dept_name,
                   a.check_in, a.check_out, a.status
            FROM attendance a
            JOIN persons p ON a.person_id = p.id
            LEFT JOIN departments d ON p.dept_id = d.id
            WHERE a.date BETWEEN ? AND ?
        """
        params = [start_date, end_date]

        if dept_id:
            query += " AND p.dept_id = ?"
            params.append(dept_id)

        query += " ORDER BY a.date, p.name"

        c.execute(query, params)
        records = c.fetchall()

        # 生成Excel文件
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output)
        worksheet = workbook.add_worksheet()

        # 设置表头
        headers = ["日期", "姓名", "部门", "上班时间", "下班时间", "状态"]
        for col, header in enumerate(headers):
            worksheet.write(0, col, header)

        # 填充数据
        for row, record in enumerate(records, 1):
            worksheet.write(row, 0, record["date"])
            worksheet.write(row, 1, record["person_name"])
            worksheet.write(row, 2, record["dept_name"])
            worksheet.write(row, 3, record["check_in"] if record["check_in"] else "")
            worksheet.write(row, 4, record["check_out"] if record["check_out"] else "")
            worksheet.write(row, 5, record["status"])

        workbook.close()
        output.seek(0)

        filename = f"考勤数据_{start_date}_至_{end_date}"
        if dept_id:
            c.execute("SELECT name FROM departments WHERE id=?", (dept_id,))
            dept_name = c.fetchone()["name"]
            filename += f"_{dept_name}"
        filename += ".xlsx"

        return StreamingResponse(
            output,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    finally:
        conn.close()

# 考勤统计路由
@router.get("/attendance/report")
async def attendance_report(
    request: Request,
    username: str = Depends(get_current_admin_user),
    start_date: str = Query(...),
    end_date: str = Query(...),
    dept_id: int = Query(None)
):
    conn = sqlite3.connect(DATABASE)
    try:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()

        # 部门考勤统计
        dept_query = """
            SELECT d.name as dept_name,
                   COUNT(DISTINCT a.person_id) as person_count,
                   SUM(CASE WHEN a.status = '正常' THEN 1 ELSE 0 END) as normal_count,
                   SUM(CASE WHEN a.status = '迟到' THEN 1 ELSE 0 END) as late_count,
                   SUM(CASE WHEN a.status = '早退' THEN 1 ELSE 0 END) as early_count,
                   SUM(CASE WHEN a.status = '缺勤' THEN 1 ELSE 0 END) as absent_count
            FROM attendance a
            JOIN persons p ON a.person_id = p.id
            JOIN departments d ON p.dept_id = d.id
            WHERE a.date BETWEEN ? AND ?
        """
        dept_params = [start_date, end_date]

        if dept_id:
            dept_query += " AND p.dept_id = ?"
            dept_params.append(dept_id)

        dept_query += " GROUP BY d.name ORDER BY d.name"

        c.execute(dept_query, dept_params)
        dept_stats = c.fetchall()

        # 个人考勤统计
        person_query = """
            SELECT p.name as person_name, d.name as dept_name,
                   COUNT(*) as total_days,
                   SUM(CASE WHEN a.status = '正常' THEN 1 ELSE 0 END) as normal_days,
                   ROUND(AVG(strftime('%s', a.check_in) - strftime('%s', datetime(a.date || ' 09:00:00'))) / 60, 1) as avg_late_minutes
            FROM attendance a
            JOIN persons p ON a.person_id = p.id
            LEFT JOIN departments d ON p.dept_id = d.id
            WHERE a.date BETWEEN ? AND ?
        """
        person_params = [start_date, end_date]

        if dept_id:
            person_query += " AND p.dept_id = ?"
            person_params.append(dept_id)

        person_query += " GROUP BY p.name, d.name ORDER BY avg_late_minutes DESC LIMIT 10"

        c.execute(person_query, person_params)
        person_stats = c.fetchall()

        # 获取部门列表用于筛选
        c.execute("SELECT id, name FROM departments")
        departments = c.fetchall()

        return templates.TemplateResponse("admin/attendance_report.html", {
            "request": request,
            "current_tab": "attendance",
            "dept_stats": dept_stats,
            "person_stats": person_stats,
            "departments": departments,
            "start_date": start_date,
            "end_date": end_date,
            "selected_dept": dept_id,
            "logged_in_user": username
        })
    finally:
        conn.close()

# 考勤管理路由
@router.get("/attendance")
async def manage_attendance(
    request: Request,
    username: str = Depends(get_current_admin_user),
    date: str = Query(None),
    dept_id: int = Query(None)
):
    conn = sqlite3.connect(DATABASE)
    try:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()

        query = """
            SELECT a.id, a.date, a.check_in, a.check_out, a.status,
                   p.name as person_name, d.name as dept_name
            FROM attendance a
            JOIN persons p ON a.person_id = p.id
            LEFT JOIN departments d ON p.dept_id = d.id
            WHERE 1=1
        """
        params = []

        if date:
            query += " AND a.date = ?"
            params.append(date)
        if dept_id:
            query += " AND p.dept_id = ?"
            params.append(dept_id)

        query += " ORDER BY a.date DESC, p.name"

        c.execute(query, params)
        records = c.fetchall()

        # 获取部门列表用于筛选
        c.execute("SELECT id, name FROM departments")
        departments = c.fetchall()

        return templates.TemplateResponse("admin/attendance.html", {
            "request": request,
            "current_tab": "attendance",
            "records": records,
            "departments": departments,
            "selected_date": date,
            "selected_dept": dept_id,
            "logged_in_user": username
        })
    finally:
        conn.close()

@router.post("/attendance/record")
async def record_attendance(
    request: Request,
    username: str = Depends(get_current_admin_user),
    person_id: int = Form(...),
    date: str = Form(...),
    check_in: str = Form(None),
    check_out: str = Form(None),
    status: str = Form('正常')
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()
        # 检查是否已存在记录
        c.execute("SELECT id FROM attendance WHERE person_id=? AND date=?", (person_id, date))
        existing = c.fetchone()

        if existing:
            # 更新记录
            c.execute("""
                UPDATE attendance
                SET check_in=?, check_out=?, status=?
                WHERE id=?
            """, (check_in, check_out, status, existing['id']))
        else:
            # 新增记录
            c.execute("""
                INSERT INTO attendance (person_id, date, check_in, check_out, status)
                VALUES (?, ?, ?, ?, ?)
            """, (person_id, date, check_in, check_out, status))

        conn.commit()
        return RedirectResponse(url="/admin/attendance", status_code=303)
    except Exception as e:
        conn.rollback()
        raise HTTPException(status_code=400, detail=f"记录考勤失败: {str(e)}")
    finally:
        conn.close()

@router.get("/persons/{person_id}/edit")
async def edit_person_form(
    request: Request,
    person_id: int,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    try:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        c.execute("SELECT id, name, dept_id, position, face_id FROM persons WHERE id=?", (person_id,))
        person = c.fetchone()

        c.execute("SELECT id, name FROM departments")
        departments = c.fetchall()

        if not person:
            raise HTTPException(status_code=404, detail="人员不存在")

        return templates.TemplateResponse("admin/person_form.html", {
            "request": request,
            "current_tab": "persons",
            "person": person,
            "departments": departments,
            "logged_in_user": username
        })
    finally:
        conn.close()

@router.post("/persons/{person_id}")
async def update_person(
    request: Request,
    person_id: int,
    username: str = Depends(get_current_admin_user),
    name: str = Form(...),
    dept_id: int = Form(...),
    position: str = Form(None),
    face_id: str = Form(None)
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()
        c.execute("""
            UPDATE persons
            SET name=?, dept_id=?, position=?, face_id=?
            WHERE id=?
        """, (name, dept_id, position, face_id, person_id))
        conn.commit()
        return RedirectResponse(url="/admin/persons", status_code=303)
    except sqlite3.IntegrityError:
        conn.rollback()
        return templates.TemplateResponse("admin/person_form.html", {
            "request": request,
            "current_tab": "persons",
            "person": {
                "id": person_id,
                "name": name,
                "dept_id": dept_id,
                "position": position,
                "face_id": face_id
            },
            "departments": [],
            "error": "更新人员失败",
            "logged_in_user": username
        })
    finally:
        conn.close()

@router.post("/persons/{person_id}/delete")
async def delete_person(
    person_id: int,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()
        c.execute("DELETE FROM persons WHERE id=?", (person_id,))
        conn.commit()
        return RedirectResponse(url="/admin/persons", status_code=303)
    finally:
        conn.close()

# 人员管理路由
@router.get("/persons")
async def manage_persons(
    request: Request,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    c.execute("""
        SELECT p.id, p.name, p.position, p.face_id,
               d.name as dept_name
        FROM persons p
        LEFT JOIN departments d ON p.dept_id = d.id
        ORDER BY p.id
    """)
    persons = c.fetchall()
    conn.close()

    return templates.TemplateResponse("admin/persons.html", {
        "request": request,
        "current_tab": "persons",
        "persons": persons,
        "logged_in_user": username
    })

@router.get("/persons/new")
async def new_person_form(
    request: Request,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()
    c.execute("SELECT id, name FROM departments")
    departments = c.fetchall()
    conn.close()

    return templates.TemplateResponse("admin/person_form.html", {
        "request": request,
        "current_tab": "persons",
        "person": None,
        "departments": departments,
        "logged_in_user": username
    })

@router.post("/persons")
async def create_person(
    request: Request,
    username: str = Depends(get_current_admin_user),
    name: str = Form(...),
    dept_id: int = Form(...),
    position: str = Form(None),
    face_id: str = Form(None)
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()
        c.execute("""
            INSERT INTO persons (name, dept_id, position, face_id)
            VALUES (?, ?, ?, ?)
        """, (name, dept_id, position, face_id))
        conn.commit()
        return RedirectResponse(url="/admin/persons", status_code=303)
    except sqlite3.IntegrityError:
        conn.rollback()
        return templates.TemplateResponse("admin/person_form.html", {
            "request": request,
            "current_tab": "persons",
            "person": None,
            "departments": [],
            "error": "创建人员失败",
            "logged_in_user": username
        })
    finally:
        conn.close()

@router.get("/departments/{dept_id}/edit")
async def edit_department_form(
    request: Request,
    dept_id: int,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    try:
        conn.row_factory = sqlite3.Row
        c = conn.cursor()
        c.execute("SELECT id, name, parent_id, status FROM departments WHERE id=?", (dept_id,))
        department = c.fetchone()

        c.execute("SELECT id, name FROM departments WHERE id != ?", (dept_id,))
        departments = c.fetchall()

        if not department:
            raise HTTPException(status_code=404, detail="部门不存在")

        return templates.TemplateResponse("admin/department_form.html", {
            "request": request,
            "current_tab": "departments",
            "department": department,
            "departments": departments,
            "logged_in_user": username
        })
    finally:
        conn.close()

@router.post("/departments/{dept_id}")
async def update_department(
    request: Request,
    dept_id: int,
    username: str = Depends(get_current_admin_user),
    name: str = Form(...),
    parent_id: str = Form(None),
    status: int = Form(1)
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()
        c.execute("""
            UPDATE departments
            SET name=?, parent_id=?, status=?
            WHERE id=?
        """, (name, parent_id if parent_id else None, status, dept_id))
        conn.commit()
        return RedirectResponse(url="/admin/departments", status_code=303)
    except sqlite3.IntegrityError as e:
        conn.rollback()
        error = "部门名称已存在" if "UNIQUE" in str(e) else "更新部门失败"

        c.execute("SELECT id, name FROM departments WHERE id != ?", (dept_id,))
        departments = c.fetchall()

        return templates.TemplateResponse("admin/department_form.html", {
            "request": request,
            "current_tab": "departments",
            "department": {
                "id": dept_id,
                "name": name,
                "parent_id": parent_id,
                "status": status
            },
            "departments": departments,
            "error": error,
            "logged_in_user": username
        })
    finally:
        conn.close()

@router.post("/departments/{dept_id}/delete")
async def delete_department(
    dept_id: int,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()
        # 检查是否有子部门
        c.execute("SELECT COUNT(*) FROM departments WHERE parent_id=?", (dept_id,))
        child_count = c.fetchone()[0]

        if child_count > 0:
            raise HTTPException(
                status_code=400,
                detail="该部门下有子部门，请先删除或转移子部门"
            )

        # 检查是否有人员关联
        c.execute("SELECT COUNT(*) FROM persons WHERE dept_id=?", (dept_id,))
        person_count = c.fetchone()[0]

        if person_count > 0:
            raise HTTPException(
                status_code=400,
                detail="该部门下有人员，请先转移人员"
            )

        c.execute("DELETE FROM departments WHERE id=?", (dept_id,))
        conn.commit()
        return RedirectResponse(url="/admin/departments", status_code=303)
    finally:
        conn.close()

@router.get("/users/{user_id}/edit")
async def edit_user_form(
    request: Request,
    user_id: int,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    c.execute("SELECT id, username, email, role FROM users WHERE id=?", (user_id,))
    user = c.fetchone()
    conn.close()

    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")

    return templates.TemplateResponse("admin/user_form.html", {
        "request": request,
        "current_tab": "users",
        "user": user,
        "logged_in_user": username
    })

@router.post("/users/{user_id}")
async def update_user(
    request: Request,
    user_id: int,
    username: str = Depends(get_current_admin_user),
    form_username: str = Form(...),
    password: str = Form(None),
    email: str = Form(None),
    role: str = Form("user")
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()
        if password:
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            c.execute("""
                UPDATE users
                SET username=?, password_hash=?, email=?, role=?
                WHERE id=?
            """, (form_username, password_hash, email, role, user_id))
        else:
            c.execute("""
                UPDATE users
                SET username=?, email=?, role=?
                WHERE id=?
            """, (form_username, email, role, user_id))
        conn.commit()
        return RedirectResponse(url="/admin/users", status_code=303)
    except sqlite3.IntegrityError:
        conn.rollback()
        return templates.TemplateResponse("admin/user_form.html", {
            "request": request,
            "current_tab": "users",
            "user": {
                "id": user_id,
                "username": form_username,
                "email": email,
                "role": role
            },
            "error": "用户名已存在",
            "logged_in_user": username
        })
    finally:
        conn.close()

@router.post("/users/{user_id}/delete")
async def delete_user(
    user_id: int,
    username: str = Depends(get_current_admin_user)):
    # 防止删除最后一个管理员
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()
        c.execute("SELECT COUNT(*) FROM users WHERE role='admin'")
        admin_count = c.fetchone()[0]

        c.execute("SELECT role FROM users WHERE id=?", (user_id,))
        user_role = c.fetchone()[0]

        if user_role == 'admin' and admin_count <= 1:
            raise HTTPException(
                status_code=400,
                detail="不能删除最后一个管理员账号"
            )

        c.execute("DELETE FROM users WHERE id=?", (user_id,))
        conn.commit()
        return RedirectResponse(url="/admin/users", status_code=303)
    finally:
        conn.close()

# 部门管理路由
@router.get("/departments")
async def manage_departments(
    request: Request,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    c.execute("SELECT id, name, parent_id, status FROM departments ORDER BY id")
    departments = c.fetchall()
    conn.close()

    return templates.TemplateResponse("admin/departments.html", {
        "request": request,
        "current_tab": "departments",
        "departments": departments,
        "logged_in_user": username
    })

@router.get("/departments/new")
async def new_department_form(
    request: Request,
    username: str = Depends(get_current_admin_user)
):
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()
    c.execute("SELECT id, name FROM departments")
    departments = c.fetchall()
    conn.close()

    return templates.TemplateResponse("admin/department_form.html", {
        "request": request,
        "current_tab": "departments",
        "department": None,
        "departments": departments,
        "logged_in_user": username
    })

@router.post("/departments")
async def create_department(
    request: Request,
    username: str = Depends(get_current_admin_user),
    name: str = Form(...),
    parent_id: str = Form(None),
    status: int = Form(1)
):
    conn = sqlite3.connect(DATABASE)
    try:
        c = conn.cursor()
        c.execute("""
            INSERT INTO departments (name, parent_id, status)
            VALUES (?, ?, ?)
        """, (name, parent_id if parent_id else None, status))
        conn.commit()
        return RedirectResponse(url="/admin/departments", status_code=303)
    except sqlite3.IntegrityError as e:
        conn.rollback()
        error = "部门名称已存在" if "UNIQUE" in str(e) else "创建部门失败"
        return templates.TemplateResponse("admin/department_form.html", {
            "request": request,
            "current_tab": "departments",
            "department": None,
            "departments": [],
            "error": error,
            "logged_in_user": username
        })
    finally:
        conn.close()
