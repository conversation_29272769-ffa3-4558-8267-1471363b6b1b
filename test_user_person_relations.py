#!/usr/bin/env python3
"""
测试用户人员关联管理功能
"""
import sqlite3
from admin.user_person_relation import (
    get_all_relations,
    get_all_users,
    get_all_persons,
    get_unrelated_users,
    get_unrelated_persons,
    create_relation,
    delete_relation,
    get_user_relations_count
)

def test_all_functions():
    """测试所有用户人员关联功能"""
    print("=" * 60)
    print("测试用户人员关联管理功能")
    print("=" * 60)
    
    # 1. 测试获取所有关联
    print("\n1. 测试获取所有关联:")
    relations = get_all_relations()
    print(f"   总关联数: {len(relations)}")
    for relation in relations[:3]:  # 只显示前3个
        print(f"   - ID: {relation['id']}, 用户: {relation['username']}, 人员: {relation['person_name']}")
    
    # 2. 测试获取所有用户
    print("\n2. 测试获取所有用户:")
    users = get_all_users()
    print(f"   总用户数: {len(users)}")
    for user in users[:3]:  # 只显示前3个
        print(f"   - ID: {user['id']}, 用户名: {user['username']}, 已关联: {'是' if user['is_related'] else '否'}")
    
    # 3. 测试获取所有人员
    print("\n3. 测试获取所有人员:")
    persons = get_all_persons()
    print(f"   总人员数: {len(persons)}")
    for person in persons[:3]:  # 只显示前3个
        print(f"   - ID: {person['id']}, 姓名: {person['name']}, 已关联: {'是' if person['is_related'] else '否'}")
    
    # 4. 测试获取未关联用户
    print("\n4. 测试获取未关联用户:")
    unrelated_users = get_unrelated_users()
    print(f"   未关联用户数: {len(unrelated_users)}")
    for user in unrelated_users:
        print(f"   - ID: {user['id']}, 用户名: {user['username']}, 角色: {user.get('role', '无')}")
    
    # 5. 测试获取未关联人员
    print("\n5. 测试获取未关联人员:")
    unrelated_persons = get_unrelated_persons()
    print(f"   未关联人员数: {len(unrelated_persons)}")
    for person in unrelated_persons[:3]:  # 只显示前3个
        print(f"   - ID: {person['id']}, 姓名: {person['name']}, 工号: {person.get('employee_id', '无')}")
    
    # 6. 测试统计信息
    print("\n6. 测试统计信息:")
    stats = get_user_relations_count()
    print(f"   总关联数: {stats['total_relations']}")
    print(f"   已关联用户数: {stats['related_users']}")
    print(f"   已关联人员数: {stats['related_persons']}")
    print(f"   总用户数: {stats['total_users']}")
    print(f"   总人员数: {stats['total_persons']}")
    print(f"   未关联用户数: {stats['unrelated_users']}")
    print(f"   未关联人员数: {stats['unrelated_persons']}")
    
    # 7. 测试创建关联（如果有未关联的用户和人员）
    if unrelated_users and unrelated_persons:
        print("\n7. 测试创建关联:")
        test_user = unrelated_users[0]
        test_person = unrelated_persons[0]
        print(f"   尝试关联用户 {test_user['username']} (ID: {test_user['id']}) 与人员 {test_person['name']} (ID: {test_person['id']})")
        
        result = create_relation(test_user['id'], test_person['id'])
        print(f"   创建结果: {result}")
        
        if result['success']:
            # 8. 测试删除关联
            print("\n8. 测试删除关联:")
            print(f"   尝试删除刚创建的关联")
            delete_result = delete_relation(test_user['id'], test_person['id'])
            print(f"   删除结果: {delete_result}")
    else:
        print("\n7. 跳过创建关联测试 - 没有可用的未关联用户或人员")
        print("8. 跳过删除关联测试")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

def test_page_data():
    """测试页面数据准备"""
    print("\n" + "=" * 60)
    print("测试页面数据准备")
    print("=" * 60)
    
    # 模拟页面数据获取
    relations = get_all_relations()
    all_users = get_all_users()
    all_persons = get_all_persons()
    unrelated_users = get_unrelated_users()
    unrelated_persons = get_unrelated_persons()
    
    print(f"页面数据统计:")
    print(f"  - 关联列表: {len(relations)} 条")
    print(f"  - 所有用户: {len(all_users)} 个")
    print(f"  - 所有人员: {len(all_persons)} 个")
    print(f"  - 未关联用户: {len(unrelated_users)} 个")
    print(f"  - 未关联人员: {len(unrelated_persons)} 个")
    
    # 检查数据完整性
    print(f"\n数据完整性检查:")
    
    # 检查关联数据
    for relation in relations:
        required_fields = ['id', 'user_id', 'person_id', 'username', 'person_name', 'created_at']
        missing_fields = [field for field in required_fields if field not in relation or relation[field] is None]
        if missing_fields:
            print(f"  ⚠️  关联 ID {relation.get('id', '?')} 缺少字段: {missing_fields}")
    
    # 检查用户数据
    for user in all_users:
        required_fields = ['id', 'username']
        missing_fields = [field for field in required_fields if field not in user or user[field] is None]
        if missing_fields:
            print(f"  ⚠️  用户 ID {user.get('id', '?')} 缺少字段: {missing_fields}")
    
    # 检查人员数据
    for person in all_persons:
        required_fields = ['id', 'name']
        missing_fields = [field for field in required_fields if field not in person or person[field] is None]
        if missing_fields:
            print(f"  ⚠️  人员 ID {person.get('id', '?')} 缺少字段: {missing_fields}")
    
    print("  ✅ 数据完整性检查完成")

if __name__ == "__main__":
    test_all_functions()
    test_page_data()
