#!/usr/bin/env python3
"""
将数据库中check_type为'face'的记录转换为'check_in'和'check_out'
"""
import sqlite3
from datetime import datetime
from collections import defaultdict
from admin.db_init import DATABASE

def convert_face_records():
    """转换face类型记录为check_in/check_out"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    
    try:
        print("开始转换face类型考勤记录...")
        print("=" * 80)
        
        # 首先备份原始数据
        print("1. 创建备份表...")
        c.execute("""
            CREATE TABLE IF NOT EXISTS attendance_backup AS 
            SELECT * FROM attendance WHERE check_type = 'face'
        """)
        
        backup_count = c.execute("SELECT COUNT(*) FROM attendance_backup").fetchone()[0]
        print(f"   已备份 {backup_count} 条face类型记录到 attendance_backup 表")
        
        # 获取所有face类型的记录
        c.execute("""
            SELECT a.id, a.person_id, a.timestamp, a.status, p.name as person_name
            FROM attendance a
            LEFT JOIN persons p ON a.person_id = p.id
            WHERE a.check_type = 'face'
            ORDER BY a.timestamp
        """)
        face_records = c.fetchall()
        
        print(f"2. 分析 {len(face_records)} 条face类型记录...")
        
        # 按人员和日期分组
        person_daily_records = defaultdict(list)
        
        for record in face_records:
            timestamp = datetime.strptime(record['timestamp'], '%Y-%m-%d %H:%M:%S')
            date = timestamp.date()
            person_id = record['person_id']
            
            person_daily_records[(person_id, date)].append({
                'id': record['id'],
                'timestamp': timestamp,
                'status': record['status'],
                'person_name': record['person_name']
            })
        
        # 生成转换计划
        conversion_plan = []
        stats = {
            'single_records': 0,
            'double_records': 0,
            'multiple_records': 0,
            'check_in_conversions': 0,
            'check_out_conversions': 0
        }
        
        for (person_id, date), records in person_daily_records.items():
            records.sort(key=lambda x: x['timestamp'])
            person_name = records[0]['person_name'] or f"Person_{person_id}"
            
            if len(records) == 1:
                # 单条记录：根据时间判断是上班还是下班
                record = records[0]
                hour = record['timestamp'].hour
                
                if hour < 12:  # 上午记录视为上班打卡
                    new_type = 'check_in'
                    stats['check_in_conversions'] += 1
                else:  # 下午记录视为下班打卡
                    new_type = 'check_out'
                    stats['check_out_conversions'] += 1
                
                conversion_plan.append({
                    'id': record['id'],
                    'new_type': new_type,
                    'timestamp': record['timestamp'],
                    'reason': f"单条记录，{hour}点{'上班' if new_type == 'check_in' else '下班'}打卡"
                })
                
                stats['single_records'] += 1
                
            elif len(records) == 2:
                # 两条记录：第一条为上班，第二条为下班
                first_record = records[0]
                second_record = records[1]
                
                conversion_plan.append({
                    'id': first_record['id'],
                    'new_type': 'check_in',
                    'timestamp': first_record['timestamp'],
                    'reason': "两条记录中的第一条，上班打卡"
                })
                
                conversion_plan.append({
                    'id': second_record['id'],
                    'new_type': 'check_out',
                    'timestamp': second_record['timestamp'],
                    'reason': "两条记录中的第二条，下班打卡"
                })
                
                stats['double_records'] += 1
                stats['check_in_conversions'] += 1
                stats['check_out_conversions'] += 1
                
            else:
                # 多条记录：第一条为上班，最后一条为下班，中间的保持face
                first_record = records[0]
                last_record = records[-1]
                
                conversion_plan.append({
                    'id': first_record['id'],
                    'new_type': 'check_in',
                    'timestamp': first_record['timestamp'],
                    'reason': "多条记录中的第一条，上班打卡"
                })
                
                conversion_plan.append({
                    'id': last_record['id'],
                    'new_type': 'check_out',
                    'timestamp': last_record['timestamp'],
                    'reason': "多条记录中的最后一条，下班打卡"
                })
                
                stats['multiple_records'] += 1
                stats['check_in_conversions'] += 1
                stats['check_out_conversions'] += 1
        
        print(f"3. 转换计划统计:")
        print(f"   - 单条记录天数: {stats['single_records']}")
        print(f"   - 两条记录天数: {stats['double_records']}")
        print(f"   - 多条记录天数: {stats['multiple_records']}")
        print(f"   - 将转换为check_in: {stats['check_in_conversions']} 条")
        print(f"   - 将转换为check_out: {stats['check_out_conversions']} 条")
        print(f"   - 总计需要转换: {len(conversion_plan)} 条记录")
        
        # 执行转换
        print(f"\n4. 开始执行转换...")
        
        converted_count = 0
        for plan in conversion_plan:
            c.execute("""
                UPDATE attendance 
                SET check_type = ? 
                WHERE id = ?
            """, (plan['new_type'], plan['id']))
            converted_count += 1
            
            if converted_count % 50 == 0:
                print(f"   已转换 {converted_count}/{len(conversion_plan)} 条记录...")
        
        # 提交事务
        conn.commit()
        
        print(f"5. 转换完成！")
        print(f"   成功转换 {converted_count} 条记录")
        
        # 验证转换结果
        print(f"\n6. 验证转换结果...")
        c.execute("SELECT check_type, COUNT(*) FROM attendance GROUP BY check_type")
        type_counts = c.fetchall()
        
        print("   转换后的check_type分布:")
        for type_name, count in type_counts:
            print(f"     - '{type_name}': {count} 条记录")
        
        # 检查是否还有face类型记录
        c.execute("SELECT COUNT(*) FROM attendance WHERE check_type = 'face'")
        remaining_face = c.fetchone()[0]
        
        if remaining_face == 0:
            print("   ✅ 所有face类型记录已成功转换")
        else:
            print(f"   ⚠️  还有 {remaining_face} 条face类型记录未转换")
        
        print(f"\n转换完成！数据库已更新。")
        print(f"备份数据保存在 attendance_backup 表中，如需回滚可使用备份数据。")
        
        return True
        
    except Exception as e:
        conn.rollback()
        print(f"转换过程中出错: {str(e)}")
        print("已回滚所有更改")
        return False
    finally:
        conn.close()

def verify_conversion():
    """验证转换结果"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    
    try:
        print("\n验证转换结果:")
        print("=" * 50)
        
        # 检查转换后的数据
        c.execute("""
            SELECT a.id, a.timestamp, a.check_type, a.status, p.name as person_name
            FROM attendance a
            LEFT JOIN persons p ON a.person_id = p.id
            WHERE a.check_type IN ('check_in', 'check_out')
            ORDER BY a.timestamp DESC
            LIMIT 10
        """)
        recent_records = c.fetchall()
        
        print("最近10条转换后的记录:")
        for record in recent_records:
            type_display = "上班打卡" if record['check_type'] == 'check_in' else "下班打卡"
            print(f"  {record['person_name']}: {record['timestamp']} | {type_display} | {record['status']}")
        
    except Exception as e:
        print(f"验证时出错: {str(e)}")
    finally:
        conn.close()

if __name__ == "__main__":
    print("face类型考勤记录转换工具")
    print("=" * 80)
    
    # 询问用户确认
    confirm = input("确定要将所有face类型记录转换为check_in/check_out吗？(y/N): ")
    
    if confirm.lower() in ['y', 'yes']:
        success = convert_face_records()
        if success:
            verify_conversion()
    else:
        print("转换已取消")
