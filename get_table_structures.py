#!/usr/bin/env python3
"""
获取指定表的详细结构信息
"""
import sqlite3

DATABASE = "face_recognition.db"

def get_table_structure(table_name):
    """获取表的详细结构信息"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    
    try:
        # 获取表结构
        c.execute(f"PRAGMA table_info({table_name})")
        columns = c.fetchall()
        
        # 获取外键信息
        c.execute(f"PRAGMA foreign_key_list({table_name})")
        foreign_keys = c.fetchall()
        
        # 创建外键映射
        fk_map = {}
        for fk in foreign_keys:
            fk_map[fk['from']] = {
                'table': fk['table'],
                'column': fk['to']
            }
        
        return columns, fk_map
        
    except Exception as e:
        print(f"获取表 {table_name} 结构时出错: {str(e)}")
        return [], {}
    finally:
        conn.close()

def format_table_structure(table_name, columns, fk_map):
    """格式化表结构为文档格式"""
    
    # 表名映射到中文说明
    table_descriptions = {
        'announcements': '通知公告表',
        'leave_approval_flows': '请假审批流程表',
        'leave_approval_logs': '请假审批日志表',
        'roles': '角色表'
    }
    
    # 字段说明映射
    field_descriptions = {
        # 通用字段
        'id': 'ID',
        'created_at': '创建时间',
        'updated_at': '更新时间',
        
        # announcements表
        'title': '公告标题',
        'content': '公告内容',
        'type': '公告类型',
        'status': '状态',
        'created_by': '创建人ID',
        'published_at': '发布时间',
        
        # leave_approval_flows表
        'request_id': '请假申请ID',
        'step_number': '审批步骤号',
        'approver_id': '审批人ID',
        'approver_type': '审批人类型',
        'action': '审批动作',
        'comments': '审批意见',
        'approved_at': '审批时间',
        
        # leave_approval_logs表
        'flow_id': '审批流程ID',
        'old_status': '原状态',
        'new_status': '新状态',
        'operator_id': '操作人ID',
        'operation': '操作类型',
        'reason': '操作原因',
        'ip_address': 'IP地址',
        
        # roles表
        'name': '角色名称',
        'description': '角色描述',
        'permissions': '权限配置'
    }
    
    result = []
    result.append(f"## {table_descriptions.get(table_name, table_name)}（{table_name}）")
    result.append("")
    result.append("| 字段名 | 数据类型 | 约束 | 说明 |")
    result.append("|--------|----------|------|------|")
    
    for col in columns:
        field_name = col['name']
        data_type = col['type']
        not_null = col['notnull']
        default_value = col['dflt_value']
        pk = col['pk']
        
        # 构建约束信息
        constraints = []
        
        if pk:
            if data_type == 'INTEGER' and pk == 1:
                constraints.append("PRIMARY KEY AUTOINCREMENT")
            else:
                constraints.append("PRIMARY KEY")
        
        if not_null and not pk:
            constraints.append("NOT NULL")
        
        if field_name in fk_map:
            fk_info = fk_map[field_name]
            constraints.append(f"FOREIGN KEY → {fk_info['table']}({fk_info['column']})")
        
        if default_value is not None:
            if default_value == 'CURRENT_TIMESTAMP':
                constraints.append("DEFAULT CURRENT_TIMESTAMP")
            else:
                constraints.append(f"DEFAULT {default_value}")
        
        constraint_str = " ".join(constraints) if constraints else ""
        description = field_descriptions.get(field_name, "")
        
        result.append(f"| {field_name} | {data_type} | {constraint_str} | {description} |")
    
    result.append("")
    return "\n".join(result)

def generate_tables_documentation():
    """生成指定表的文档"""
    tables = ['announcements', 'leave_approval_flows', 'leave_approval_logs', 'roles']
    
    print("数据库表结构文档")
    print("=" * 60)
    
    all_results = []
    all_results.append("# 数据库表结构文档")
    all_results.append("")
    all_results.append("本文档详细描述了系统中主要数据表的结构信息。")
    all_results.append("")
    
    for table_name in tables:
        print(f"\n正在处理表: {table_name}")
        columns, fk_map = get_table_structure(table_name)
        
        if columns:
            formatted_result = format_table_structure(table_name, columns, fk_map)
            all_results.append(formatted_result)
            print(f"✅ {table_name} 处理完成")
        else:
            print(f"❌ {table_name} 处理失败")
    
    return "\n".join(all_results)

if __name__ == "__main__":
    try:
        documentation = generate_tables_documentation()
        
        # 保存到文件
        with open("数据库表结构文档.md", "w", encoding="utf-8") as f:
            f.write(documentation)
        
        print(f"\n" + "=" * 60)
        print("✅ 文档生成完成！已保存到: 数据库表结构文档.md")
        
        # 也打印到控制台
        print(f"\n" + "=" * 60)
        print("📋 文档内容预览:")
        print(documentation)
        
    except Exception as e:
        print(f"生成文档时出错: {str(e)}")
        import traceback
        traceback.print_exc()
