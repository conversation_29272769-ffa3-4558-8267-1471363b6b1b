#!/usr/bin/env python3
"""
检查考勤记录数据，特别是check_type字段的值
"""
import sqlite3
from admin.db_init import DATABASE

def check_attendance_data():
    """检查考勤记录数据"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    
    try:
        print("检查考勤记录数据...")
        print("=" * 60)
        
        # 检查考勤表结构
        c.execute("PRAGMA table_info(attendance)")
        columns = c.fetchall()
        print("考勤表结构:")
        for col in columns:
            print(f"  - {col['name']}: {col['type']}")
        
        print("\n" + "=" * 60)
        
        # 获取总记录数
        c.execute("SELECT COUNT(*) as count FROM attendance")
        total_count = c.fetchone()['count']
        print(f"总考勤记录数: {total_count}")
        
        # 检查check_type字段的所有不同值
        c.execute("SELECT DISTINCT check_type, COUNT(*) as count FROM attendance GROUP BY check_type")
        check_types = c.fetchall()
        print(f"\ncheck_type字段的值分布:")
        for ct in check_types:
            print(f"  - '{ct['check_type']}': {ct['count']} 条记录")
        
        # 检查status字段的所有不同值
        c.execute("SELECT DISTINCT status, COUNT(*) as count FROM attendance GROUP BY status")
        statuses = c.fetchall()
        print(f"\nstatus字段的值分布:")
        for st in statuses:
            print(f"  - '{st['status']}': {st['count']} 条记录")
        
        # 获取最近10条记录的详细信息
        c.execute("""
            SELECT a.id, a.person_id, a.timestamp, a.check_type, a.status, a.type, a.note,
                   p.name as person_name
            FROM attendance a
            LEFT JOIN persons p ON a.person_id = p.id
            ORDER BY a.timestamp DESC
            LIMIT 10
        """)
        recent_records = c.fetchall()
        
        print(f"\n最近10条考勤记录:")
        print("-" * 100)
        print(f"{'ID':<5} {'人员':<10} {'时间':<20} {'类型':<15} {'状态':<10} {'备注':<20}")
        print("-" * 100)
        for record in recent_records:
            print(f"{record['id']:<5} {record['person_name'] or 'N/A':<10} {record['timestamp']:<20} {record['check_type'] or 'NULL':<15} {record['status'] or 'NULL':<10} {record['note'] or '':<20}")
        
        # 检查是否有NULL值
        c.execute("SELECT COUNT(*) as count FROM attendance WHERE check_type IS NULL")
        null_check_type = c.fetchone()['count']
        print(f"\ncheck_type为NULL的记录数: {null_check_type}")
        
        c.execute("SELECT COUNT(*) as count FROM attendance WHERE status IS NULL")
        null_status = c.fetchone()['count']
        print(f"status为NULL的记录数: {null_status}")
        
        # 检查是否有空字符串
        c.execute("SELECT COUNT(*) as count FROM attendance WHERE check_type = ''")
        empty_check_type = c.fetchone()['count']
        print(f"check_type为空字符串的记录数: {empty_check_type}")
        
        c.execute("SELECT COUNT(*) as count FROM attendance WHERE status = ''")
        empty_status = c.fetchone()['count']
        print(f"status为空字符串的记录数: {empty_status}")
        
    except Exception as e:
        print(f"检查考勤数据时出错: {str(e)}")
    finally:
        conn.close()

if __name__ == "__main__":
    check_attendance_data()
