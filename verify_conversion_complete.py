#!/usr/bin/env python3
"""
验证face类型记录转换的完整性
"""
import sqlite3
from admin.db_init import DATABASE

def verify_conversion_complete():
    """验证转换的完整性"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    c = conn.cursor()
    
    try:
        print("验证face类型记录转换完整性")
        print("=" * 60)
        
        # 1. 检查当前check_type分布
        c.execute("SELECT check_type, COUNT(*) as count FROM attendance GROUP BY check_type ORDER BY check_type")
        current_distribution = c.fetchall()
        
        print("1. 当前check_type分布:")
        total_records = 0
        for row in current_distribution:
            print(f"   - '{row['check_type']}': {row['count']} 条记录")
            total_records += row['count']
        print(f"   总计: {total_records} 条记录")
        
        # 2. 检查备份表
        c.execute("SELECT COUNT(*) as count FROM attendance_backup")
        backup_count = c.fetchone()['count']
        print(f"\n2. 备份表记录数: {backup_count} 条")
        
        # 3. 检查是否还有face类型记录
        c.execute("SELECT COUNT(*) as count FROM attendance WHERE check_type = 'face'")
        remaining_face = c.fetchone()['count']
        
        if remaining_face == 0:
            print("3. ✅ 确认：没有剩余的face类型记录")
        else:
            print(f"3. ⚠️  警告：还有 {remaining_face} 条face类型记录")
        
        # 4. 按日期统计转换后的记录
        c.execute("""
            SELECT DATE(timestamp) as date, 
                   SUM(CASE WHEN check_type = 'check_in' THEN 1 ELSE 0 END) as check_ins,
                   SUM(CASE WHEN check_type = 'check_out' THEN 1 ELSE 0 END) as check_outs
            FROM attendance 
            WHERE check_type IN ('check_in', 'check_out')
            GROUP BY DATE(timestamp)
            ORDER BY date DESC
            LIMIT 5
        """)
        daily_stats = c.fetchall()
        
        print(f"\n4. 最近5天的打卡统计:")
        print("   日期        | 上班打卡 | 下班打卡")
        print("   " + "-" * 35)
        for row in daily_stats:
            print(f"   {row['date']} |    {row['check_ins']:2d}    |    {row['check_outs']:2d}")
        
        # 5. 检查数据完整性
        c.execute("""
            SELECT p.name, 
                   COUNT(CASE WHEN a.check_type = 'check_in' THEN 1 END) as check_ins,
                   COUNT(CASE WHEN a.check_type = 'check_out' THEN 1 END) as check_outs
            FROM persons p
            LEFT JOIN attendance a ON p.id = a.person_id 
            WHERE a.check_type IN ('check_in', 'check_out')
            GROUP BY p.id, p.name
            ORDER BY p.name
        """)
        person_stats = c.fetchall()
        
        print(f"\n5. 各人员打卡统计:")
        print("   人员    | 上班打卡 | 下班打卡")
        print("   " + "-" * 30)
        for row in person_stats:
            print(f"   {row['name']:<6} |    {row['check_ins']:2d}    |    {row['check_outs']:2d}")
        
        # 6. 检查时间分布合理性
        c.execute("""
            SELECT 
                CASE 
                    WHEN CAST(strftime('%H', timestamp) AS INTEGER) < 12 THEN '上午'
                    ELSE '下午'
                END as time_period,
                check_type,
                COUNT(*) as count
            FROM attendance 
            WHERE check_type IN ('check_in', 'check_out')
            GROUP BY time_period, check_type
            ORDER BY time_period, check_type
        """)
        time_distribution = c.fetchall()
        
        print(f"\n6. 时间分布合理性检查:")
        for row in time_distribution:
            type_name = "上班打卡" if row['check_type'] == 'check_in' else "下班打卡"
            print(f"   {row['time_period']} {type_name}: {row['count']} 条")
        
        print(f"\n转换验证完成！")
        print("=" * 60)
        
        return remaining_face == 0
        
    except Exception as e:
        print(f"验证时出错: {str(e)}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    success = verify_conversion_complete()
    if success:
        print("✅ 转换验证通过，所有face类型记录已成功转换为check_in/check_out")
    else:
        print("❌ 转换验证失败，请检查数据")
