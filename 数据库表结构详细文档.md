# 数据库表结构详细文档

本文档详细描述了系统中主要数据表的结构信息，包括字段名、数据类型、约束条件和说明。

---

## 通知公告表（announcements）

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | 公告ID |
| title | TEXT | NOT NULL | 公告标题 |
| content | TEXT | NOT NULL | 公告内容 |
| type | TEXT | DEFAULT 'normal' | 公告类型（normal-普通，important-重要，urgent-紧急） |
| status | TEXT | DEFAULT 'active' | 状态（active-激活，inactive-停用，draft-草稿） |
| priority | INTEGER | DEFAULT 0 | 优先级（数字越大优先级越高） |
| start_date | TEXT |  | 生效开始日期 |
| end_date | TEXT |  | 生效结束日期 |
| created_by | INTEGER | FOREIGN KEY | 创建人ID（关联users表） |
| created_at | TEXT | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TEXT | DEFAULT CURRENT_TIMESTAMP | 更新时间 |

---

## 请假审批流程表（leave_approval_flows）

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | 流程ID |
| department_id | INTEGER | NOT NULL FOREIGN KEY | 部门ID（关联departments表） |
| approver_id | INTEGER | NOT NULL FOREIGN KEY | 审批人ID（关联users表） |
| level | INTEGER | DEFAULT 1 | 审批级别（1-一级审批，2-二级审批，等） |
| created_at | TEXT | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

---

## 请假审批日志表（leave_approval_logs）

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | 日志ID |
| request_id | INTEGER | NOT NULL FOREIGN KEY | 请假申请ID（关联leave_requests表） |
| approver_id | INTEGER | NOT NULL FOREIGN KEY | 审批人ID（关联users表） |
| status | TEXT | NOT NULL | 审批状态（approved-批准，rejected-拒绝，pending-待审批） |
| comments | TEXT |  | 审批意见 |
| created_at | TEXT | DEFAULT CURRENT_TIMESTAMP | 审批时间 |

---

## 角色表（roles）

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | 角色ID |
| name | TEXT | NOT NULL | 角色名称（如：超级管理员，人事管理员，部门主管，普通员工） |
| description | TEXT |  | 角色描述 |
| permissions | TEXT |  | 权限配置（JSON格式存储权限信息） |
| created_at | TEXT | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

---

## 字段约束说明

### 主键约束
- **PRIMARY KEY AUTOINCREMENT**: 主键自动递增，用于唯一标识每条记录

### 外键约束
- **FOREIGN KEY**: 外键约束，确保数据的引用完整性
  - `created_by` → `users(id)`: 创建人必须是有效用户
  - `department_id` → `departments(id)`: 部门必须是有效部门
  - `approver_id` → `users(id)`: 审批人必须是有效用户
  - `request_id` → `leave_requests(id)`: 请假申请必须是有效申请

### 非空约束
- **NOT NULL**: 字段不能为空，必须提供值

### 默认值约束
- **DEFAULT 'value'**: 字段的默认值
- **DEFAULT CURRENT_TIMESTAMP**: 自动设置为当前时间戳

---

## 数据类型说明

### INTEGER
- 整数类型，用于存储数字ID、级别、优先级等

### TEXT
- 文本类型，用于存储字符串数据
- 可以存储任意长度的文本内容
- 在SQLite中，TEXT类型非常灵活，可以存储JSON格式数据

---

## 业务逻辑说明

### 通知公告表（announcements）
- 支持不同类型和优先级的公告
- 可以设置生效时间范围
- 支持草稿、激活、停用等状态管理

### 请假审批流程表（leave_approval_flows）
- 为不同部门配置不同的审批流程
- 支持多级审批（通过level字段）
- 一个部门可以有多个审批人和多个审批级别

### 请假审批日志表（leave_approval_logs）
- 记录每个请假申请的审批历史
- 支持审批意见的记录
- 可以追溯完整的审批过程

### 角色表（roles）
- 定义系统中的用户角色
- 通过JSON格式的permissions字段灵活配置权限
- 支持角色的描述和说明

---

## 注意事项

1. **时间字段**: 所有时间字段使用TEXT类型存储，格式为ISO 8601标准
2. **JSON权限**: roles表的permissions字段存储JSON格式的权限配置
3. **外键关系**: 确保在删除关联数据时考虑外键约束
4. **索引建议**: 建议在经常查询的外键字段上创建索引以提高性能
