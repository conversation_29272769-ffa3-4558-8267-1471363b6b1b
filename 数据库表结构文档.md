# 数据库表结构文档

本文档详细描述了系统中主要数据表的结构信息。

## 通知公告表（announcements）

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | ID |
| title | TEXT | NOT NULL | 公告标题 |
| content | TEXT | NOT NULL | 公告内容 |
| type | TEXT | DEFAULT 'normal' | 公告类型 |
| status | TEXT | DEFAULT 'active' | 状态 |
| priority | INTEGER | DEFAULT 0 |  |
| start_date | TEXT |  |  |
| end_date | TEXT |  |  |
| created_by | INTEGER | FOREIGN KEY → users(id) | 创建人ID |
| created_at | TEXT | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TEXT | DEFAULT CURRENT_TIMESTAMP | 更新时间 |

## 请假审批流程表（leave_approval_flows）

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | ID |
| department_id | INTEGER | NOT NULL FOREIGN KEY → departments(id) |  |
| approver_id | INTEGER | NOT NULL FOREIGN KEY → users(id) | 审批人ID |
| level | INTEGER | DEFAULT 1 |  |
| created_at | TEXT | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

## 请假审批日志表（leave_approval_logs）

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | ID |
| request_id | INTEGER | NOT NULL FOREIGN KEY → leave_requests(id) | 请假申请ID |
| approver_id | INTEGER | NOT NULL FOREIGN KEY → users(id) | 审批人ID |
| status | TEXT | NOT NULL | 状态 |
| comments | TEXT |  | 审批意见 |
| created_at | TEXT | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

## 角色表（roles）

| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | INTEGER | PRIMARY KEY AUTOINCREMENT | ID |
| name | TEXT | NOT NULL | 角色名称 |
| description | TEXT |  | 角色描述 |
| permissions | TEXT |  | 权限配置 |
| created_at | TEXT | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
