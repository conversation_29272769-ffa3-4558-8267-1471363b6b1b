#!/usr/bin/env python3
"""
简化版数据库表检查工具
"""
import sqlite3
import os

DATABASE = "face_recognition.db"

def check_all_tables():
    """检查所有表的数据情况"""
    print("数据库表检查报告")
    print("=" * 60)
    
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()
    
    try:
        # 获取所有表
        c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in c.fetchall()]
        
        print(f"数据库中共有 {len(tables)} 个表\n")
        
        empty_tables = []
        non_empty_tables = []
        
        # 检查每个表的数据量
        for table in sorted(tables):
            try:
                c.execute(f"SELECT COUNT(*) FROM {table}")
                count = c.fetchone()[0]
                
                if count == 0:
                    empty_tables.append(table)
                else:
                    non_empty_tables.append((table, count))
                    
            except Exception as e:
                print(f"检查表 {table} 时出错: {e}")
        
        # 显示有数据的表
        print(f"有数据的表 ({len(non_empty_tables)} 个):")
        print("-" * 40)
        for table, count in non_empty_tables:
            print(f"  {table:<25} | {count:>6} 条记录")
        
        # 显示空表
        print(f"\n空表 ({len(empty_tables)} 个):")
        print("-" * 40)
        for table in empty_tables:
            print(f"  {table}")
        
        return empty_tables, non_empty_tables
        
    finally:
        conn.close()

def check_table_structure(table_name):
    """检查表结构"""
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()
    
    try:
        c.execute(f"PRAGMA table_info({table_name})")
        columns = c.fetchall()
        
        print(f"\n表 {table_name} 的结构:")
        print("  字段名          | 类型      | 非空 | 默认值")
        print("  " + "-" * 45)
        for col in columns:
            name = col[1]
            type_name = col[2]
            not_null = "是" if col[3] else "否"
            default = col[4] if col[4] is not None else ""
            print(f"  {name:<15} | {type_name:<8} | {not_null:<3} | {default}")
            
    except Exception as e:
        print(f"检查表结构时出错: {e}")
    finally:
        conn.close()

def search_in_files(table_name, search_dirs=['admin', 'user', 'templates', 'main.py']):
    """在文件中搜索表名"""
    found_files = []
    
    for search_item in search_dirs:
        if os.path.isfile(search_item):
            # 单个文件
            try:
                with open(search_item, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    if table_name in content:
                        found_files.append(search_item)
            except:
                pass
        elif os.path.isdir(search_item):
            # 目录
            for root, _, files in os.walk(search_item):
                if '__pycache__' in root:
                    continue
                for file in files:
                    if file.endswith(('.py', '.html', '.js')):
                        file_path = os.path.join(root, file)
                        try:
                            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                content = f.read()
                                if table_name in content:
                                    found_files.append(file_path)
                        except:
                            pass
    
    return found_files

def analyze_empty_tables():
    """分析空表的使用情况"""
    empty_tables, non_empty_tables = check_all_tables()
    
    if not empty_tables:
        print("\n✅ 所有表都有数据，没有空表需要分析")
        return
    
    print(f"\n详细分析空表:")
    print("=" * 60)
    
    for table in empty_tables:
        print(f"\n📋 表名: {table}")
        print("-" * 30)
        
        # 检查表结构
        check_table_structure(table)
        
        # 搜索代码引用
        found_files = search_in_files(table)
        
        if found_files:
            print(f"\n  在代码中被引用: ✅ 是")
            print(f"  引用文件 ({len(found_files)} 个):")
            for file in found_files[:5]:  # 只显示前5个
                print(f"    - {file}")
            if len(found_files) > 5:
                print(f"    ... 还有 {len(found_files) - 5} 个文件")
        else:
            print(f"\n  在代码中被引用: ❌ 否")
        
        # 给出建议
        if found_files:
            if any(keyword in table.lower() for keyword in ['user', 'person', 'attendance', 'department']):
                suggestion = "🟢 重要表，建议保留（可能是预留功能）"
            else:
                suggestion = "🟡 在代码中使用，建议保留"
        else:
            if any(keyword in table.lower() for keyword in ['test', 'temp', 'backup', 'old']):
                suggestion = "🔴 可能是测试表，建议删除"
            else:
                suggestion = "🟠 未使用，需要人工确认是否删除"
        
        print(f"  建议: {suggestion}")

if __name__ == "__main__":
    try:
        analyze_empty_tables()
        
        print(f"\n" + "=" * 60)
        print("检查完成！")
        
    except Exception as e:
        print(f"检查过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
