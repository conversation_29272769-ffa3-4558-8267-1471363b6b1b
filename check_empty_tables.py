#!/usr/bin/env python3
"""
检查数据库中的空表，并分析它们是否在代码中被使用
"""
import sqlite3
import os
import re
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from admin.db_init import DATABASE
except ImportError:
    DATABASE = "face_recognition.db"

def get_all_tables():
    """获取数据库中的所有表"""
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()
    
    try:
        c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [row[0] for row in c.fetchall()]
        return tables
    finally:
        conn.close()

def check_table_data():
    """检查每个表的数据量"""
    conn = sqlite3.connect(DATABASE)
    c = conn.cursor()
    
    table_stats = {}
    
    try:
        tables = get_all_tables()
        
        for table in tables:
            c.execute(f"SELECT COUNT(*) FROM {table}")
            count = c.fetchone()[0]
            
            # 获取表结构
            c.execute(f"PRAGMA table_info({table})")
            columns = c.fetchall()
            
            table_stats[table] = {
                'count': count,
                'columns': [col[1] for col in columns],
                'column_info': columns
            }
    
    finally:
        conn.close()
    
    return table_stats

def search_table_usage_in_code(table_name):
    """在代码中搜索表的使用情况"""
    usage_info = {
        'files': [],
        'sql_operations': [],
        'references': []
    }
    
    # 搜索的文件扩展名
    extensions = ['.py', '.html', '.js']
    
    # 搜索的目录
    search_dirs = ['admin', 'user', 'templates', 'static', '.']
    
    # 可能的SQL操作模式
    sql_patterns = [
        rf'INSERT\s+INTO\s+{table_name}',
        rf'SELECT\s+.*\s+FROM\s+{table_name}',
        rf'UPDATE\s+{table_name}',
        rf'DELETE\s+FROM\s+{table_name}',
        rf'CREATE\s+TABLE\s+{table_name}',
        rf'DROP\s+TABLE\s+{table_name}',
        rf'ALTER\s+TABLE\s+{table_name}',
        rf'"{table_name}"',
        rf"'{table_name}'",
        rf'`{table_name}`'
    ]
    
    for search_dir in search_dirs:
        if not os.path.exists(search_dir):
            continue
            
        for root, dirs, files in os.walk(search_dir):
            # 跳过__pycache__目录
            if '__pycache__' in root:
                continue
                
            for file in files:
                if any(file.endswith(ext) for ext in extensions):
                    file_path = os.path.join(root, file)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                            
                            # 检查是否包含表名
                            if table_name in content:
                                usage_info['files'].append(file_path)
                                
                                # 检查具体的SQL操作
                                for pattern in sql_patterns:
                                    matches = re.findall(pattern, content, re.IGNORECASE)
                                    if matches:
                                        usage_info['sql_operations'].extend([
                                            f"{file_path}: {match}" for match in matches
                                        ])
                                
                                # 查找包含表名的行
                                lines = content.split('\n')
                                for i, line in enumerate(lines, 1):
                                    if table_name in line:
                                        usage_info['references'].append(
                                            f"{file_path}:{i}: {line.strip()}"
                                        )
                    
                    except Exception as e:
                        # 忽略读取错误
                        pass
    
    return usage_info

def analyze_database_tables():
    """分析数据库表的使用情况"""
    print("数据库表使用情况分析")
    print("=" * 80)
    
    # 获取表统计信息
    table_stats = check_table_data()
    
    # 分类表
    empty_tables = []
    non_empty_tables = []
    
    for table, stats in table_stats.items():
        if stats['count'] == 0:
            empty_tables.append(table)
        else:
            non_empty_tables.append(table)
    
    print(f"数据库中共有 {len(table_stats)} 个表")
    print(f"其中 {len(empty_tables)} 个表为空，{len(non_empty_tables)} 个表有数据")
    
    # 显示有数据的表
    print(f"\n有数据的表 ({len(non_empty_tables)} 个):")
    print("-" * 50)
    for table in sorted(non_empty_tables):
        count = table_stats[table]['count']
        print(f"  {table:<25} | {count:>6} 条记录")
    
    # 分析空表
    print(f"\n空表分析 ({len(empty_tables)} 个):")
    print("=" * 80)
    
    for table in sorted(empty_tables):
        print(f"\n表名: {table}")
        print("-" * 40)
        
        # 显示表结构
        columns = table_stats[table]['columns']
        print(f"字段: {', '.join(columns)}")
        
        # 搜索代码中的使用情况
        usage = search_table_usage_in_code(table)
        
        if usage['files']:
            print(f"在代码中被引用: 是")
            print(f"引用文件数: {len(usage['files'])}")
            
            # 显示主要引用文件
            main_files = [f for f in usage['files'] if not f.endswith('.pyc')][:3]
            for file in main_files:
                print(f"  - {file}")
            
            if len(usage['files']) > 3:
                print(f"  ... 还有 {len(usage['files']) - 3} 个文件")
            
            # 显示SQL操作
            if usage['sql_operations']:
                print(f"SQL操作: {len(usage['sql_operations'])} 个")
                for op in usage['sql_operations'][:2]:
                    print(f"  - {op}")
                if len(usage['sql_operations']) > 2:
                    print(f"  ... 还有 {len(usage['sql_operations']) - 2} 个操作")
        else:
            print(f"在代码中被引用: 否")
        
        # 判断是否真的在使用
        is_used = len(usage['files']) > 0
        is_important = any(keyword in table.lower() for keyword in 
                          ['user', 'person', 'attendance', 'department', 'setting'])
        
        if is_used:
            if is_important:
                status = "🟢 重要且在使用"
            else:
                status = "🟡 在使用但可能不重要"
        else:
            if is_important:
                status = "🟠 重要但未使用（可能是预留功能）"
            else:
                status = "🔴 未使用且可能可以删除"
        
        print(f"状态: {status}")
    
    return empty_tables, table_stats

def generate_cleanup_recommendations(empty_tables, table_stats):
    """生成清理建议"""
    print(f"\n清理建议:")
    print("=" * 50)
    
    can_delete = []
    should_keep = []
    need_review = []
    
    for table in empty_tables:
        usage = search_table_usage_in_code(table)
        
        if not usage['files']:
            # 没有在代码中被引用
            if any(keyword in table.lower() for keyword in 
                   ['test', 'temp', 'backup', 'old']):
                can_delete.append(table)
            else:
                need_review.append(table)
        else:
            # 在代码中被引用
            should_keep.append(table)
    
    if can_delete:
        print(f"\n🔴 建议删除的表 ({len(can_delete)} 个):")
        for table in can_delete:
            print(f"  - {table} (未在代码中使用)")
    
    if need_review:
        print(f"\n🟠 需要人工审查的表 ({len(need_review)} 个):")
        for table in need_review:
            print(f"  - {table} (未在代码中使用，但可能是重要功能)")
    
    if should_keep:
        print(f"\n🟢 建议保留的表 ({len(should_keep)} 个):")
        for table in should_keep:
            print(f"  - {table} (在代码中被引用)")
    
    return {
        'can_delete': can_delete,
        'need_review': need_review,
        'should_keep': should_keep
    }

if __name__ == "__main__":
    try:
        empty_tables, table_stats = analyze_database_tables()
        recommendations = generate_cleanup_recommendations(empty_tables, table_stats)
        
        print(f"\n总结:")
        print("-" * 30)
        print(f"空表总数: {len(empty_tables)}")
        print(f"建议删除: {len(recommendations['can_delete'])}")
        print(f"需要审查: {len(recommendations['need_review'])}")
        print(f"建议保留: {len(recommendations['should_keep'])}")
        
    except Exception as e:
        print(f"分析过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
