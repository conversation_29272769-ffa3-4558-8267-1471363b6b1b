{% extends "user/base.html" %}

{% block title %}考勤记录{% endblock %}

{% block page_title %}考勤记录{% endblock %}

{% block page_actions %}
<form class="d-flex" method="get" action="/user/attendance">
    <div class="input-group me-2">
        <span class="input-group-text">开始日期</span>
        <input type="date" class="form-control" name="start_date" value="{{ start_date }}">
    </div>
    <div class="input-group me-2">
        <span class="input-group-text">结束日期</span>
        <input type="date" class="form-control" name="end_date" value="{{ end_date }}">
    </div>
    <button type="submit" class="btn btn-primary">
        <i class="bi bi-search"></i> 查询
    </button>
</form>
{% endblock %}

{% block content %}
{% if person %}
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-header">
                <i class="bi bi-person me-2"></i>
                人员信息
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <small class="text-muted">姓名:</small>
                    <span class="ms-2">{{ person.name }}</span>
                </div>
                <div class="mb-2">
                    <small class="text-muted">工号:</small>
                    <span class="ms-2">{{ person.employee_id if person.employee_id else '未设置' }}</span>
                </div>
                <div class="mb-2">
                    <small class="text-muted">部门:</small>
                    <span class="ms-2">{{ person.department_name if person.department_name else '未分配' }}</span>
                </div>
                <div class="mb-2">
                    <small class="text-muted">职位:</small>
                    <span class="ms-2">{{ person.position if person.position else '未设置' }}</span>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card h-100">
            <div class="card-header">
                <i class="bi bi-bar-chart-line me-2"></i>
                考勤统计
                <span class="ms-2 text-muted small">{{ start_date }} 至 {{ end_date }}</span>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-4 mb-3">
                        <div class="p-3 rounded bg-success-light">
                            <h3 class="mb-0">{{ attendance_stats.normal_days }}</h3>
                            <p class="mb-0">正常出勤天数</p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="p-3 rounded bg-warning-light">
                            <h3 class="mb-0">{{ attendance_stats.late_count }}</h3>
                            <p class="mb-0">迟到次数</p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="p-3 rounded bg-danger-light">
                            <h3 class="mb-0">{{ attendance_stats.early_leave_count }}</h3>
                            <p class="mb-0">早退次数</p>
                        </div>
                    </div>
                </div>
                <div class="mt-3" style="max-width: 300px; margin: 0 auto;">
                    <canvas id="attendanceChart" height="150"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <i class="bi bi-calendar-check me-2"></i>
        考勤记录
    </div>
    <div class="card-body">
        {% if records %}
        <div class="table-responsive">
            <table class="table table-striped table-hover datatable">
                <thead>
                    <tr>
                        <th>日期</th>
                        <th>时间</th>
                        <th>类型</th>
                        <th>状态</th>
                        <th>备注</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in records %}
                    <tr>
                        <td>{{ record.timestamp.split(' ')[0] }}</td>
                        <td>{{ record.timestamp.split(' ')[1] }}</td>
                        <td>
                            {% if record.check_type == 'check_in' %}
                            <span class="badge bg-primary">上班打卡</span>
                            {% elif record.check_type == 'check_out' %}
                            <span class="badge bg-success">下班打卡</span>
                            {% else %}
                            <span class="badge bg-secondary">未知</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if record.status == 'normal' %}
                            <span class="badge bg-success">正常</span>
                            {% elif record.status == 'late' %}
                            <span class="badge bg-warning">迟到</span>
                            {% elif record.status == 'early_leave' %}
                            <span class="badge bg-warning">早退</span>
                            {% elif record.status == 'absent' %}
                            <span class="badge bg-danger">缺勤</span>
                            {% else %}
                            <span class="badge bg-secondary">{{ record.status }}</span>
                            {% endif %}
                        </td>
                        <td>{{ record.note if record.note else '-' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-calendar-x text-muted" style="font-size: 3rem;"></i>
            <p class="mt-3">所选时间范围内暂无考勤记录</p>
        </div>
        {% endif %}
    </div>
</div>
{% else %}
<div class="card">
    <div class="card-body text-center py-5">
        <i class="bi bi-exclamation-circle text-warning" style="font-size: 3rem;"></i>
        <h5 class="mt-3">未找到关联的人员信息</h5>
        <p class="text-muted">请联系管理员关联您的用户账号与人员信息</p>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化DataTable
        if ($.fn.DataTable) {
            $('.datatable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/zh.json'
                },
                order: [[0, 'desc'], [1, 'desc']], // 按日期和时间降序排序
                responsive: true
            });
        }

        // 考勤统计图表
        {% if person and attendance_stats %}
        const ctx = document.getElementById('attendanceChart').getContext('2d');
        new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['正常出勤', '迟到', '早退'],
                datasets: [{
                    data: [
                        {{ attendance_stats.normal_days }},
                        {{ attendance_stats.late_count }},
                        {{ attendance_stats.early_leave_count }}
                    ],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.7)',
                        'rgba(255, 193, 7, 0.7)',
                        'rgba(220, 53, 69, 0.7)'
                    ],
                    borderColor: [
                        'rgba(40, 167, 69, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(220, 53, 69, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        {% endif %}
    });
</script>
{% endblock %}
